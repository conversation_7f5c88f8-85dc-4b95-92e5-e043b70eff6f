/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChatWidget } from '../ChatWidget';
import { ChatWidgetConfig } from '../types';
import * as formTools from '../form-tools';

// Mock the API
jest.mock('../api', () => ({
  ChatAPI: jest.fn().mockImplementation(() => ({
    createSession: jest.fn().mockResolvedValue({
      success: true,
      data: { sessionId: 'test-session-123' }
    }),
    sendMessage: jest.fn(),
    sendToolResults: jest.fn().mockResolvedValue({
      success: true,
      message: 'Form interaction completed successfully!'
    })
  }))
}));

// Mock form tools
jest.mock('../form-tools', () => ({
  executeFormTool: jest.fn(),
  getFormToolDefinitions: jest.fn().mockReturnValue([])
}));

const mockExecuteFormTool = formTools.executeFormTool as jest.MockedFunction<typeof formTools.executeFormTool>;

describe('ChatWidget Form Integration', () => {
  const defaultConfig: ChatWidgetConfig = {
    baseURL: 'http://localhost:3000',
    botSlug: 'test-bot',
    enableFormTools: true,
    position: 'bottom-right',
    theme: 'light',
    language: 'en'
  };

  beforeEach(() => {
    // Create a test form in the DOM
    document.body.innerHTML = `
      <form id="testForm">
        <input type="text" id="firstName" name="firstName" required placeholder="First Name" />
        <input type="email" id="email" name="email" required placeholder="Email" />
        <button type="submit">Submit</button>
      </form>
    `;

    jest.clearAllMocks();
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  it('should render chat widget with form tools enabled', () => {
    render(<ChatWidget config={defaultConfig} />);
    
    // Chat button should be present
    const chatButton = screen.getByRole('button');
    expect(chatButton).toBeInTheDocument();
  });

  it('should execute form tools when AI requests them', async () => {
    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    // Mock AI response with tool calls
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      toolCalls: [
        {
          id: 'tool-call-1',
          type: 'function',
          function: {
            name: 'fill_field',
            arguments: JSON.stringify({
              selector: '#firstName',
              value: 'John Doe'
            })
          }
        }
      ]
    });

    // Mock form tool execution
    mockExecuteFormTool.mockResolvedValueOnce({
      success: true,
      result: {
        success: true,
        message: 'Successfully filled field: #firstName',
        value: 'John Doe'
      }
    });

    render(<ChatWidget config={defaultConfig} />);
    
    // Open chat widget
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    // Wait for session initialization
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    // Send a message
    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Fill in my name as John Doe' } });
    fireEvent.click(sendButton);

    // Wait for tool execution
    await waitFor(() => {
      expect(mockExecuteFormTool).toHaveBeenCalledWith('fill_field', {
        selector: '#firstName',
        value: 'John Doe'
      });
    });

    // Check that the form field was actually filled
    const firstNameInput = document.getElementById('firstName') as HTMLInputElement;
    expect(firstNameInput.value).toBe('John Doe');
  });

  it('should handle multiple tool calls in sequence', async () => {
    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    // Mock AI response with multiple tool calls
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      toolCalls: [
        {
          id: 'tool-call-1',
          type: 'function',
          function: {
            name: 'fill_field',
            arguments: JSON.stringify({
              selector: '#firstName',
              value: 'John'
            })
          }
        },
        {
          id: 'tool-call-2',
          type: 'function',
          function: {
            name: 'fill_field',
            arguments: JSON.stringify({
              selector: '#email',
              value: '<EMAIL>'
            })
          }
        }
      ]
    });

    // Mock form tool executions
    mockExecuteFormTool
      .mockResolvedValueOnce({
        success: true,
        result: {
          success: true,
          message: 'Successfully filled field: #firstName',
          value: 'John'
        }
      })
      .mockResolvedValueOnce({
        success: true,
        result: {
          success: true,
          message: 'Successfully filled field: #email',
          value: '<EMAIL>'
        }
      });

    render(<ChatWidget config={defaultConfig} />);
    
    // Open chat widget and send message
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Fill in my details' } });
    fireEvent.click(sendButton);

    // Wait for both tool executions
    await waitFor(() => {
      expect(mockExecuteFormTool).toHaveBeenCalledTimes(2);
    });

    expect(mockExecuteFormTool).toHaveBeenNthCalledWith(1, 'fill_field', {
      selector: '#firstName',
      value: 'John'
    });
    expect(mockExecuteFormTool).toHaveBeenNthCalledWith(2, 'fill_field', {
      selector: '#email',
      value: '<EMAIL>'
    });
  });

  it('should handle form tool execution errors gracefully', async () => {
    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      toolCalls: [
        {
          id: 'tool-call-1',
          type: 'function',
          function: {
            name: 'fill_field',
            arguments: JSON.stringify({
              selector: '#nonexistent',
              value: 'test'
            })
          }
        }
      ]
    });

    // Mock form tool execution failure
    mockExecuteFormTool.mockResolvedValueOnce({
      success: true,
      result: {
        success: false,
        message: 'Element not found: #nonexistent'
      }
    });

    render(<ChatWidget config={defaultConfig} />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Fill nonexistent field' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(mockExecuteFormTool).toHaveBeenCalled();
    });

    // Should show error message in chat
    await waitFor(() => {
      expect(screen.getByText(/Element not found/)).toBeInTheDocument();
    });
  });

  it('should not execute form tools when disabled', async () => {
    const configWithoutFormTools = {
      ...defaultConfig,
      enableFormTools: false
    };

    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      message: 'Regular response without tools'
    });

    render(<ChatWidget config={configWithoutFormTools} />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Fill in my name' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(screen.getByText(/Regular response without tools/)).toBeInTheDocument();
    });

    // Form tools should not be executed
    expect(mockExecuteFormTool).not.toHaveBeenCalled();
  });

  it('should display tool execution progress in chat', async () => {
    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      toolCalls: [
        {
          id: 'tool-call-1',
          type: 'function',
          function: {
            name: 'get_form_summary',
            arguments: JSON.stringify({
              formSelector: '#testForm'
            })
          }
        }
      ]
    });

    mockExecuteFormTool.mockResolvedValueOnce({
      success: true,
      result: {
        success: true,
        message: 'Retrieved form summary',
        value: {
          totalFields: 3,
          requiredFields: 2,
          filledFields: 0,
          formValid: false
        }
      }
    });

    render(<ChatWidget config={defaultConfig} />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'What is the current form status?' } });
    fireEvent.click(sendButton);

    // Should show tool execution message
    await waitFor(() => {
      expect(screen.getByText(/get_form_summary/)).toBeInTheDocument();
      expect(screen.getByText(/Retrieved form summary/)).toBeInTheDocument();
    });
  });

  it('should handle malformed tool call arguments', async () => {
    const mockSendMessage = require('../api').ChatAPI.mock.results[0].value.sendMessage;
    
    mockSendMessage.mockResolvedValueOnce({
      success: true,
      toolCalls: [
        {
          id: 'tool-call-1',
          type: 'function',
          function: {
            name: 'fill_field',
            arguments: 'invalid json'
          }
        }
      ]
    });

    render(<ChatWidget config={defaultConfig} />);
    
    const chatButton = screen.getByRole('button');
    fireEvent.click(chatButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText(/type your message/i)).toBeInTheDocument();
    });

    const input = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test malformed arguments' } });
    fireEvent.click(sendButton);

    // Should handle the error gracefully
    await waitFor(() => {
      expect(screen.getByText(/Failed to execute/)).toBeInTheDocument();
    });
  });
});

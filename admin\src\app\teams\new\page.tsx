'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useTeamsStore } from '@/stores/teams'
import type { TeamCreateRequest } from '@/types'

interface TeamFormData {
  name: string
  description: string
  isActive: boolean
}

export default function NewTeamPage() {
  const router = useRouter()
  const { createTeam, isLoading, error, clearError } = useTeamsStore()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<TeamFormData>({
    defaultValues: {
      name: '',
      description: '',
      isActive: true,
    },
  })

  const isActive = watch('isActive')

  const onSubmit = async (data: TeamFormData) => {
    setIsSubmitting(true)
    clearError()

    try {
      const teamData: TeamCreateRequest = {
        name: data.name.trim(),
        description: data.description.trim() || undefined,
        isActive: data.isActive,
      }

      const success = await createTeam(teamData)
      if (success) {
        router.push('/teams')
      }
    } catch (err) {
      console.error('Error creating team:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-4 mb-6">
          <Link href="/teams">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Teams
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Team</h1>
            <p className="text-muted-foreground">
              Add a new team to organize your agents and bots
            </p>
          </div>
        </div>

        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Details
              </CardTitle>
              <CardDescription>
                Enter the basic information for the new team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Team Name *</Label>
                  <Input
                    id="name"
                    {...register('name', {
                      required: 'Team name is required',
                      minLength: {
                        value: 2,
                        message: 'Team name must be at least 2 characters',
                      },
                      maxLength: {
                        value: 100,
                        message: 'Team name must be less than 100 characters',
                      },
                    })}
                    placeholder="Enter team name"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...register('description', {
                      maxLength: {
                        value: 500,
                        message: 'Description must be less than 500 characters',
                      },
                    })}
                    placeholder="Enter team description (optional)"
                    rows={3}
                    className={errors.description ? 'border-red-500' : ''}
                  />
                  {errors.description && (
                    <p className="text-red-500 text-sm">{errors.description.message}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={isActive}
                    onCheckedChange={(checked) => setValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Active Team</Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Active teams can have agents and bots assigned to them
                </p>

                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting || isLoading}
                    className="flex-1"
                  >
                    {isSubmitting || isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Create Team
                      </>
                    )}
                  </Button>
                  <Link href="/teams">
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

export interface ChatWidgetConfig {
  baseURL: string;
  botSlug?: string;
  position?: 'bottom-right' | 'bottom-left';
  theme?: 'light' | 'dark';
  language?: 'en' | 'ms-MY';
  enableFormTools?: boolean;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
  imageUrl?: string;
  agentName?: string;
  sources?: TextResult[];
  audioUrl?: string;
  fileUrl?: string;
  fileName?: string;
}

export interface TextResult {
  text: string;
  type: 'vector' | 'graph' | string;
  document_id?: string | null;
  score: number;
  wordCount: number;
  metadata?: {
    title?: string;
    url?: string;
    source?: string;
    chunk_index?: number;
    document_type?: string;
    [key: string]: any;
  };
}

export interface IntegrationStatus {
  whatsappEnabled: boolean;
  phoneNumber: string | null;
  facebookEnabled: boolean;
  pageId: string | null;
}

export interface VoiceRecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  error: string | null;
}

export interface UploadedImage {
  url: string;
  file: File;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  answer?: string;
  sources?: TextResult[];
  usage?: any;
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string;
  };
}

export interface ToolCallResult {
  toolCallId: string;
  success: boolean;
  result: any;
  error?: string;
}

export interface FormToolExecutionRequest {
  toolCall: ToolCall;
  sessionId: string;
}

export interface FormToolExecutionResponse {
  toolCallId: string;
  success: boolean;
  result: any;
  error?: string;
}

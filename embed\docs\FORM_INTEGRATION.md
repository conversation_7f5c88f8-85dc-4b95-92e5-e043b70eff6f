# Chat-Enabled Form Integration Guide

This guide explains how to integrate the AI chat widget with web forms to create intelligent form assistance capabilities.

## Overview

The chat-enabled form system allows AI assistants to interact with web forms through natural language commands. Users can ask the AI to fill fields, check form status, validate inputs, and guide them through form completion.

## Features

- **Form Field Manipulation**: Fill text inputs, select options, check boxes, and select radio buttons
- **Form Querying**: Find elements by ID, name, class, CSS selectors, or XPath
- **Form Validation**: Check form validity and identify missing required fields
- **Form Analysis**: Get comprehensive form summaries and current state
- **Natural Language Interface**: Users interact through conversational commands
- **Error Handling**: Graceful handling of invalid selectors and form errors

## Quick Start

### 1. Basic Integration

```html
<!DOCTYPE html>
<html>
<head>
    <title>Form with AI Assistant</title>
</head>
<body>
    <!-- Your form -->
    <form id="registrationForm">
        <input type="text" id="firstName" name="firstName" required placeholder="First Name" />
        <input type="email" id="email" name="email" required placeholder="Email" />
        <select id="country" name="country" required>
            <option value="">Select Country</option>
            <option value="us">United States</option>
            <option value="ca">Canada</option>
        </select>
        <button type="submit">Submit</button>
    </form>

    <!-- Chat Widget Integration -->
    <script src="path/to/halal-chat-widget.js"></script>
    <script>
        HalalChatWidget.init({
            baseURL: 'http://localhost:16001',
            botSlug: 'form-assistant',
            enableFormTools: true, // Enable form interaction
            position: 'bottom-right',
            theme: 'light',
            language: 'en'
        });
    </script>
</body>
</html>
```

### 2. Configuration Options

```javascript
HalalChatWidget.init({
    baseURL: 'http://localhost:16001',
    botSlug: 'form-assistant',
    enableFormTools: true,        // Enable/disable form tools
    position: 'bottom-right',     // 'bottom-right' | 'bottom-left'
    theme: 'light',              // 'light' | 'dark'
    language: 'en'               // 'en' | 'ms-MY'
});
```

## Available Form Tools

### 1. Query Elements
Find elements on the page using various selectors.

**AI Usage**: "What fields are available in this form?"
```javascript
// Tool: query_elements
{
    selector: "input, select, textarea",
    queryType: "css"
}
```

### 2. Fill Fields
Fill form fields with specified values.

**AI Usage**: "Fill in my <NAME_EMAIL>"
```javascript
// Tool: fill_field
{
    selector: "#email",
    value: "<EMAIL>"
}
```

### 3. Select Options
Select options in dropdown or multi-select elements.

**AI Usage**: "Select United States as my country"
```javascript
// Tool: select_option
{
    selector: "#country",
    values: ["us"]
}
```

### 4. Get Field Values
Retrieve current values from form fields.

**AI Usage**: "What's currently in the name field?"
```javascript
// Tool: get_field_value
{
    selector: "#firstName"
}
```

### 5. Get Form Data
Get all data from a form.

**AI Usage**: "Show me all the current form data"
```javascript
// Tool: get_form_data
{
    formSelector: "#registrationForm"
}
```

### 6. Validate Form
Check form validity and get validation status.

**AI Usage**: "Is this form ready to submit?"
```javascript
// Tool: validate_form
{
    formSelector: "#registrationForm"
}
```

### 7. Get Empty Required Fields
Find required fields that are empty or invalid.

**AI Usage**: "What required fields are still empty?"
```javascript
// Tool: get_empty_required_fields
{
    formSelector: "#registrationForm"
}
```

### 8. Get Form Summary
Get comprehensive form analysis.

**AI Usage**: "Give me a summary of this form"
```javascript
// Tool: get_form_summary
{
    formSelector: "#registrationForm"
}
```

## Sample Conversations

### Basic Form Filling
```
User: "Fill in my name as John Smith"
AI: I'll fill in your name for you.
[Executes: fill_field with selector "#firstName" or "[name='firstName']"]
AI: ✅ Successfully filled the name field with "John Smith".

User: "Set my <NAME_EMAIL>"
AI: I'll update your email address.
[Executes: fill_field with selector "#email"]
AI: ✅ Email field updated to "<EMAIL>".
```

### Form Guidance
```
User: "Help me complete this form"
AI: I'll help you complete the form. Let me first check what fields need to be filled.
[Executes: get_empty_required_fields]
AI: I found 3 required fields that need to be completed:
- First Name (text input)
- Email Address (email input)  
- Country (dropdown selection)

Would you like me to help you fill these out one by one?

User: "Yes, my name is Sarah Johnson"
AI: [Executes: fill_field for firstName]
AI: ✅ Great! I've filled in "Sarah Johnson" as your first name. 
What's your email address?
```

### Form Validation
```
User: "Is my form ready to submit?"
AI: Let me check your form for you.
[Executes: validate_form]
AI: I found some issues with your form:
❌ Email field is required but empty
❌ Country selection is required

Would you like me to help you complete these fields?

User: "Fill my <NAME_EMAIL> and select Canada"
AI: [Executes: fill_field for email and select_option for country]
AI: ✅ Perfect! I've updated:
- Email: <EMAIL>
- Country: Canada

Your form is now complete and ready to submit! 🎉
```

### Advanced Form Analysis
```
User: "What's the current status of this form?"
AI: [Executes: get_form_summary]
AI: Here's your form summary:
📊 **Form Status**
- Total fields: 8
- Required fields: 4
- Completed fields: 6
- Remaining required: 2

📝 **Missing Required Fields:**
- Phone Number
- Terms & Conditions agreement

📋 **Current Values:**
- Name: John Smith ✅
- Email: <EMAIL> ✅
- Country: United States ✅
- Age: 25 ✅

Would you like me to help complete the remaining fields?
```

## Integration Patterns

### 1. Progressive Form Completion
Guide users through forms step by step:

```javascript
// The AI can automatically detect form structure and guide users
// through completion in a logical order
```

### 2. Form Validation Assistant
Real-time validation and error correction:

```javascript
// AI can check form validity and provide specific guidance
// on fixing validation errors
```

### 3. Smart Form Pre-filling
Use user context to pre-fill forms:

```javascript
// AI can remember user preferences and auto-fill common fields
```

### 4. Accessibility Enhancement
Provide voice-guided form completion:

```javascript
// Combined with voice features, users can complete forms hands-free
```

## Best Practices

### 1. Form Structure
- Use semantic HTML with proper labels
- Include `id` and `name` attributes on form elements
- Use `required` attribute for mandatory fields
- Provide clear placeholder text

```html
<label for="email">Email Address *</label>
<input type="email" id="email" name="email" required 
       placeholder="<EMAIL>" />
```

### 2. Error Handling
- The system gracefully handles missing elements
- Invalid selectors return helpful error messages
- Form validation errors are clearly communicated

### 3. Security Considerations
- File inputs cannot be programmatically filled for security
- All form interactions are logged for debugging
- User consent should be obtained for form assistance

### 4. Performance
- Form tools execute quickly with minimal DOM queries
- Batch operations when possible
- Provide user feedback during tool execution

## Troubleshooting

### Common Issues

1. **Form tools not working**
   - Ensure `enableFormTools: true` in configuration
   - Check that form elements have proper selectors
   - Verify the chat widget is properly initialized

2. **Elements not found**
   - Check element IDs and names are correct
   - Ensure elements exist when tools are executed
   - Use browser dev tools to verify selectors

3. **Form validation issues**
   - Ensure HTML5 validation attributes are properly set
   - Check for custom validation that might interfere
   - Test form submission manually first

### Debug Mode
Enable debug logging to troubleshoot issues:

```javascript
// Add to browser console to see detailed logs
localStorage.setItem('chat-widget-debug', 'true');
```

## API Reference

### Configuration
```typescript
interface ChatWidgetConfig {
  baseURL: string;
  botSlug?: string;
  enableFormTools?: boolean;  // Enable form interaction tools
  position?: 'bottom-right' | 'bottom-left';
  theme?: 'light' | 'dark';
  language?: 'en' | 'ms-MY';
}
```

### Form Tool Results
```typescript
interface ToolExecutionResult {
  success: boolean;
  result: any;
  error?: string;
}
```

## Examples

See the complete working example in `embed/public/sample.html` which demonstrates:
- Complex form with various input types
- Real-time chat assistance
- Form validation and completion guidance
- Error handling and user feedback

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the sample implementation
3. Test with the provided example form
4. Check browser console for error messages

The chat-enabled form system provides a powerful way to enhance user experience and reduce form abandonment through intelligent assistance.

## Shared Resources

The form handler library is shared between the embed widget and the front/ application:

- **embed/src/form-handler.ts** - Copy for embed widget
- **front/src/lib/form-handler.ts** - Original shared library
- **embed/src/form-tools.ts** - Tool definitions and execution logic

To update the shared library, modify `front/src/lib/form-handler.ts` and copy changes to `embed/src/form-handler.ts`.

## Server Configuration

The server automatically enables form tools for web platform sessions. Form tools are included in the AI's available tools when:
- Session platform is 'web' or undefined
- The chat widget has `enableFormTools: true`

Form tool execution flow:
1. AI decides to use form tools
2. Server returns tool calls to frontend
3. Frontend executes tools using FormHandler
4. Results are sent back to server
5. AI continues conversation with tool results

# Teams Management

This module provides comprehensive team management functionality for organizing users (agents) and bots into teams.

## Features

### ✅ Implemented
- **Team CRUD Operations**: Create, read, update, and delete teams
- **Team Listing**: View all teams with search and filtering
- **Team Details**: View team information with member counts
- **Team Members**: Display users and bots assigned to each team
- **Navigation Integration**: Teams menu item in sidebar
- **Responsive UI**: Mobile-friendly interface with proper loading states
- **Error Handling**: Comprehensive error handling and user feedback

### 🔄 Pending Implementation
- **Team Assignment in User Management**: Add team selection to user forms
- **Team Assignment in Bot Management**: Add team selection to bot forms
- **Bulk Operations**: Assign multiple users/bots to teams at once
- **Team Permissions**: Role-based access control within teams
- **Team Analytics**: Usage statistics and performance metrics

## File Structure

```
admin/src/app/teams/
├── README.md                 # This documentation
├── page.tsx                  # Teams listing page
├── new/
│   └── page.tsx             # Create new team page
└── [id]/
    ├── page.tsx             # Team detail page
    └── edit/
        └── page.tsx         # Edit team page
```

## API Integration

The teams UI integrates with the following backend endpoints:

- `GET /api/admin/teams` - List all teams
- `POST /api/admin/teams` - Create new team
- `GET /api/admin/teams/:id` - Get team details with members
- `PUT /api/admin/teams/:id` - Update team
- `DELETE /api/admin/teams/:id` - Delete team

## State Management

Teams state is managed using Zustand store (`@/stores/teams.ts`) with the following features:

- **Optimistic Updates**: UI updates immediately with server sync
- **Error Handling**: Centralized error state management
- **Loading States**: Loading indicators for all operations
- **Caching**: Efficient data fetching and caching

## UI Components

### Custom Components
- **Teams List**: Searchable table with action buttons
- **Team Form**: Reusable form for create/edit operations
- **Team Detail**: Comprehensive team information display

### Shared UI Components
- **Badge**: Status indicators (Active/Inactive)
- **Switch**: Toggle controls for team status
- **Textarea**: Multi-line text input for descriptions
- **Card**: Consistent layout containers
- **Button**: Action buttons with loading states

## Usage Examples

### Creating a Team
1. Navigate to `/teams`
2. Click "Add Team" button
3. Fill in team name and optional description
4. Set active status
5. Click "Create Team"

### Viewing Team Members
1. Navigate to `/teams`
2. Click on a team name or the shield icon
3. View team details with member lists
4. See user and bot counts

### Editing a Team
1. From team detail page, click "Edit Team"
2. Update team information
3. Click "Update Team"

## Integration Points

### With User Management
- Users can be assigned to teams via the `teamId` field
- Team assignment will be added to user forms

### With Bot Management
- Bots can be assigned to teams via the `teamId` field
- Team assignment will be added to bot forms

### With Session Management
- Sessions can be filtered by team
- Agent assignment can respect team boundaries

## Next Steps

1. **Add team selection to user management forms**
2. **Add team selection to bot management forms**
3. **Implement team-based session filtering**
4. **Add team assignment bulk operations**
5. **Implement team-based permissions**

## Testing

To test the teams functionality:

1. Start the admin application
2. Navigate to `/teams`
3. Create a new team
4. Edit the team details
5. View team information
6. Delete the team

All operations should work seamlessly with proper error handling and loading states.

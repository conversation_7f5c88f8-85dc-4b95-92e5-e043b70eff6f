import { useState, useRef, useCallback } from 'react';
import { VoiceRecordingState } from './types';
import { ChatAPI } from './api';

interface UseVoiceRecordingProps {
  api: ChatAPI;
  onTranscription?: (text: string) => void;
}

export function useVoiceRecording({ api, onTranscription }: UseVoiceRecordingProps) {
  const [state, setState] = useState<VoiceRecordingState>({
    isRecording: false,
    isProcessing: false,
    error: null,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);

  // Check if voice recording is supported
  const isSupported = useCallback(() => {
    if (typeof window === 'undefined') return false;
    
    return !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      window.MediaRecorder
    );
  }, []);

  const processAudio = useCallback(
    async (audioBlob: Blob) => {
      try {
        setState(prev => ({ ...prev, isProcessing: true, error: null }));

        if (!audioBlob || audioBlob.size === 0) {
          throw new Error('No audio data recorded');
        }

        // Create audio file
        const audioFile = new File([audioBlob], 'recording.webm', {
          type: audioBlob.type || 'audio/webm',
        });

        // Upload and transcribe
        const response = await api.uploadFile(audioFile);

        if (!response.success) {
          throw new Error(response.error || 'Transcription failed');
        }

        if (response.data?.transcription && onTranscription) {
          onTranscription(response.data.transcription);
        } else if (!response.data?.transcription) {
          setState(prev => ({
            ...prev,
            error: 'No speech detected in the recording',
            isProcessing: false,
          }));
          return;
        }

        setState(prev => ({ ...prev, isProcessing: false }));
      } catch (error) {
        console.error('Audio processing failed:', error);
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : 'Processing failed',
          isProcessing: false,
        }));
      }
    },
    [api, onTranscription]
  );

  const startRecording = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, error: null }));

      if (!isSupported()) {
        throw new Error('Voice recording is not supported in this browser');
      }

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
      });

      streamRef.current = stream;

      // Determine supported MIME type
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/mp4';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = ''; // Let browser choose
          }
        }
      }

      // Create MediaRecorder
      const mediaRecorder = mimeType
        ? new MediaRecorder(stream, { mimeType })
        : new MediaRecorder(stream);

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // Handle data available
      mediaRecorder.ondataavailable = event => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: mimeType || 'audio/webm',
        });
        processAudio(audioBlob);
        
        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };

      // Handle errors
      mediaRecorder.onerror = event => {
        console.error('MediaRecorder error:', event);
        setState(prev => ({
          ...prev,
          error: 'Recording error occurred',
          isRecording: false,
        }));
      };

      // Start recording
      mediaRecorder.start();
      setState(prev => ({ ...prev, isRecording: true }));
    } catch (error) {
      console.error('Failed to start recording:', error);
      let errorMessage = 'Failed to start recording';
      
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'Microphone permission denied. Please allow microphone access.';
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone.';
        } else {
          errorMessage = error.message;
        }
      }

      setState(prev => ({
        ...prev,
        error: errorMessage,
        isRecording: false,
      }));
    }
  }, [isSupported, processAudio]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && state.isRecording) {
      mediaRecorderRef.current.stop();
      setState(prev => ({ ...prev, isRecording: false }));
    }
  }, [state.isRecording]);

  const toggleRecording = useCallback(() => {
    if (state.isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [state.isRecording, startRecording, stopRecording]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    isSupported: isSupported(),
    startRecording,
    stopRecording,
    toggleRecording,
    clearError,
  };
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Integration Example - AI Chat Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
        }

        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
        }

        .instructions {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .instructions h3 {
            color: #0c5460;
            margin-top: 0;
        }

        .instructions ul {
            margin-bottom: 0;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .form-section {
            margin-bottom: 25px;
        }

        .form-section h3 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input {
            width: auto;
            margin: 0;
        }

        .submit-btn {
            background: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .submit-btn:hover {
            background: #2980b9;
        }

        .example-commands {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .example-commands h3 {
            color: #495057;
            margin-top: 0;
        }

        .command {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            margin: 5px 0;
            display: block;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Form Assistant Demo</h1>
        <p class="subtitle">Experience intelligent form completion with natural language commands</p>

        <div class="instructions">
            <h3>💬 How to Use the AI Assistant</h3>
            <p>Click the chat button in the bottom-right corner and try these commands:</p>
            <ul>
                <li><strong>"Fill in my name as John Smith"</strong> - Automatically fills the name field</li>
                <li><strong>"Set my <NAME_EMAIL>"</strong> - Updates the email field</li>
                <li><strong>"What fields are still empty?"</strong> - Shows missing required fields</li>
                <li><strong>"Help me complete this form"</strong> - Step-by-step guidance</li>
                <li><strong>"Is this form ready to submit?"</strong> - Validates the entire form</li>
                <li><strong>"Select Premium plan"</strong> - Chooses radio button options</li>
                <li><strong>"Check the newsletter subscription"</strong> - Handles checkboxes</li>
            </ul>
        </div>

        <form id="demoForm" novalidate>
            <!-- Personal Information -->
            <div class="form-section">
                <h3>👤 Personal Information</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="fullName">Full Name *</label>
                        <input type="text" id="fullName" name="fullName" required placeholder="Enter your full name">
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" placeholder="+****************">
                    </div>
                    <div class="form-group">
                        <label for="age">Age</label>
                        <input type="number" id="age" name="age" min="18" max="100" placeholder="25">
                    </div>
                </div>
            </div>

            <!-- Preferences -->
            <div class="form-section">
                <h3>⚙️ Preferences</h3>
                
                <div class="form-group">
                    <label for="country">Country *</label>
                    <select id="country" name="country" required>
                        <option value="">Select your country</option>
                        <option value="us">United States</option>
                        <option value="ca">Canada</option>
                        <option value="uk">United Kingdom</option>
                        <option value="au">Australia</option>
                        <option value="de">Germany</option>
                        <option value="fr">France</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Subscription Plan *</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="radio" id="planBasic" name="plan" value="basic" required>
                            <label for="planBasic">Basic ($9.99/month)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="planPremium" name="plan" value="premium" required>
                            <label for="planPremium">Premium ($19.99/month)</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="radio" id="planPro" name="plan" value="pro" required>
                            <label for="planPro">Pro ($39.99/month)</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Notifications</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="emailNotifications" name="notifications" value="email">
                            <label for="emailNotifications">Email Notifications</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="smsNotifications" name="notifications" value="sms">
                            <label for="smsNotifications">SMS Notifications</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="newsletter" name="notifications" value="newsletter">
                            <label for="newsletter">Newsletter</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="comments">Additional Comments</label>
                    <textarea id="comments" name="comments" rows="4" placeholder="Any additional information you'd like to share..."></textarea>
                </div>
            </div>

            <!-- Agreement -->
            <div class="form-section">
                <h3>📋 Agreement</h3>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="terms" name="terms" required>
                        <label for="terms">I agree to the Terms of Service *</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="privacy" name="privacy" required>
                        <label for="privacy">I agree to the Privacy Policy *</label>
                    </div>
                </div>
            </div>

            <button type="submit" class="submit-btn">🚀 Complete Registration</button>
        </form>

        <div class="example-commands">
            <h3>🎯 Try These Example Commands</h3>
            <p>Copy and paste these into the chat to see the AI in action:</p>
            
            <span class="command">"Fill my name as Sarah Johnson and <NAME_EMAIL>"</span>
            <span class="command">"I'm 28 years old and from Canada"</span>
            <span class="command">"Select the Premium plan for me"</span>
            <span class="command">"Enable email notifications and newsletter"</span>
            <span class="command">"What required fields are still missing?"</span>
            <span class="command">"Check both agreement checkboxes"</span>
            <span class="command">"Is my form complete and ready to submit?"</span>
        </div>
    </div>

    <!-- Halal Chat Widget Integration -->
    <script src="../dist/halal-chat-widget.umd.js"></script>
    <script>
        // Initialize the chat widget with form tools enabled
        HalalChatWidget.init({
            baseURL: 'http://localhost:16001',
            botSlug: 'form-assistant',
            position: 'bottom-right',
            theme: 'light',
            language: 'en',
            enableFormTools: true // This enables the form interaction capabilities
        });

        // Form submission handler
        document.getElementById('demoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            alert('🎉 Form submitted successfully!\n\nData: ' + JSON.stringify(data, null, 2));
        });

        // Real-time validation feedback
        const requiredFields = document.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.style.borderColor = '#27ae60';
                } else {
                    this.style.borderColor = '#e74c3c';
                }
            });
        });
    </script>
</body>
</html>

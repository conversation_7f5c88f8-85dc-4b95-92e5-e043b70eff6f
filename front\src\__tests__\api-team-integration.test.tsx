import { render, screen, waitFor } from '@testing-library/react'
import { api } from '@/lib/api'
import { useTeamsStore } from '@/stores/teams'
import type { Team, Bot, User } from '@/types'

// Mock fetch globally
global.fetch = jest.fn()

const mockFetch = fetch as jest.MockedFunction<typeof fetch>

describe('API Team Integration', () => {
  beforeEach(() => {
    mockFetch.mockClear()
  })

  describe('Teams API', () => {
    it('should fetch teams with correct headers', async () => {
      const mockTeams: Team[] = [
        {
          id: 1,
          siteId: 1,
          name: 'Support Team',
          description: 'Customer support agents',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 2,
          siteId: 1,
          name: 'Sales Team',
          description: 'Sales representatives',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockTeams }),
      } as Response)

      const result = await api.admin.listTeams()

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/admin/teams',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockTeams)
    })

    it('should create team with correct payload', async () => {
      const newTeam = {
        name: 'New Team',
        description: 'A new team',
        isActive: true,
      }

      const createdTeam: Team = {
        id: 3,
        siteId: 1,
        ...newTeam,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: createdTeam }),
      } as Response)

      const result = await api.admin.createTeam(newTeam)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/admin/teams',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(newTeam),
        })
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(createdTeam)
    })
  })

  describe('Team Assignment API', () => {
    it('should assign user to team', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      await api.admin.assignUserToTeam(1, 2)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/admin/users/1/team',
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({ teamId: 2 }),
        })
      )
    })

    it('should remove user from team', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      await api.admin.removeUserFromTeam(1)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/admin/users/1/team',
        expect.objectContaining({
          method: 'DELETE',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      )
    })

    it('should assign bot to team', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      await api.admin.assignBotToTeam(1, 2)

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/admin/bots/1/team',
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({ teamId: 2 }),
        })
      )
    })
  })

  describe('Session API with Team Filtering', () => {
    it('should fetch sessions with team filter', async () => {
      const mockSessions = [
        {
          id: 'session-1',
          platform: 'web',
          status: 'active',
          isHandedOver: false,
          createdAt: '2024-01-01T00:00:00Z',
          agentTeamId: 1,
          agentTeamName: 'Support Team',
          botTeamId: 1,
          botTeamName: 'Support Team',
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockSessions }),
      } as Response)

      const result = await api.sessions.getActiveSessions(1)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/sessions/active?teamId=1'),
        expect.any(Object)
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockSessions)
    })

    it('should fetch sessions without team filter', async () => {
      const mockSessions = [
        {
          id: 'session-1',
          platform: 'web',
          status: 'active',
          isHandedOver: false,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockSessions }),
      } as Response)

      const result = await api.sessions.getActiveSessions()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/sessions/active'),
        expect.any(Object)
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockSessions)
    })

    it('should get available agents for session', async () => {
      const mockAgents = [
        {
          id: 1,
          username: 'agent1',
          teamId: 1,
          teamName: 'Support Team',
          isActive: true,
          isOnline: true,
        },
      ]

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: mockAgents }),
      } as Response)

      const result = await api.sessions.getAvailableAgents('session-1', true)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/sessions/session-1/available-agents?enforceTeamConstraints=true'),
        expect.any(Object)
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockAgents)
    })
  })

  describe('Teams Store Integration', () => {
    it('should handle team assignment operations', async () => {
      const { assignUserToTeam, assignBotToTeam } = useTeamsStore.getState()

      // Mock successful assignment
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      const result = await assignUserToTeam(1, 2)

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin/users/1/team'),
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify({ teamId: 2 }),
        })
      )
    })

    it('should handle team removal operations', async () => {
      const { assignUserToTeam } = useTeamsStore.getState()

      // Mock successful removal
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      } as Response)

      const result = await assignUserToTeam(1, null)

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin/users/1/team'),
        expect.objectContaining({
          method: 'DELETE',
        })
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Team constraint violation' }),
      } as Response)

      try {
        await api.sessions.takeoverSession('session-1')
      } catch (error) {
        expect(error).toBeDefined()
      }
    })

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      try {
        await api.admin.listTeams()
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
      }
    })
  })
})

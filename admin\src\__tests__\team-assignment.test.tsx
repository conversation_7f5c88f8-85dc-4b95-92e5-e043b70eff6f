import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { TeamSelect } from '@/components/ui/team-select'
import { useTeamsStore } from '@/stores/teams'
import type { Team } from '@/types'

// Mock the teams store
jest.mock('@/stores/teams', () => ({
  useTeamsStore: jest.fn(),
}))

const mockTeams: Team[] = [
  {
    id: 1,
    siteId: 1,
    name: 'Development Team',
    description: 'Frontend and backend developers',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    siteId: 1,
    name: 'Support Team',
    description: 'Customer support agents',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 3,
    siteId: 1,
    name: 'Inactive Team',
    description: 'This team is inactive',
    isActive: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
]

describe('TeamSelect Component', () => {
  const mockOnChange = jest.fn()
  const mockFetchTeams = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      isLoading: false,
      fetchTeams: mockFetchTeams,
    })
  })

  it('should render team select with placeholder', () => {
    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
        placeholder="Select a team"
      />
    )

    expect(screen.getByText('Team')).toBeInTheDocument()
    expect(screen.getByDisplayValue('')).toBeInTheDocument()
  })

  it('should display only active teams', () => {
    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
      />
    )

    const select = screen.getByRole('combobox')
    
    // Check that active teams are present
    expect(screen.getByText('Development Team')).toBeInTheDocument()
    expect(screen.getByText('Support Team')).toBeInTheDocument()
    
    // Check that inactive team is not present
    expect(screen.queryByText('Inactive Team')).not.toBeInTheDocument()
  })

  it('should call onChange when team is selected', () => {
    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
      />
    )

    const select = screen.getByRole('combobox')
    fireEvent.change(select, { target: { value: '1' } })

    expect(mockOnChange).toHaveBeenCalledWith(1)
  })

  it('should call onChange with null when empty option is selected', () => {
    render(
      <TeamSelect
        value={1}
        onChange={mockOnChange}
      />
    )

    const select = screen.getByRole('combobox')
    fireEvent.change(select, { target: { value: '' } })

    expect(mockOnChange).toHaveBeenCalledWith(null)
  })

  it('should show selected team value', () => {
    render(
      <TeamSelect
        value={1}
        onChange={mockOnChange}
      />
    )

    const select = screen.getByRole('combobox') as HTMLSelectElement
    expect(select.value).toBe('1')
  })

  it('should fetch teams on mount when teams array is empty', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      isLoading: false,
      fetchTeams: mockFetchTeams,
    })

    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
      />
    )

    expect(mockFetchTeams).toHaveBeenCalled()
  })

  it('should show loading state', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      isLoading: true,
      fetchTeams: mockFetchTeams,
    })

    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
      />
    )

    expect(screen.getByText('Loading teams...')).toBeInTheDocument()
  })

  it('should show no teams message when no active teams available', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      isLoading: false,
      fetchTeams: mockFetchTeams,
    })

    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
      />
    )

    expect(screen.getByText('No active teams available. Create a team first.')).toBeInTheDocument()
  })

  it('should support required prop', () => {
    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
        required={true}
      />
    )

    const select = screen.getByRole('combobox')
    expect(select).toHaveAttribute('required')
    expect(screen.getByText('*')).toBeInTheDocument()
  })

  it('should support custom label', () => {
    render(
      <TeamSelect
        value={null}
        onChange={mockOnChange}
        label="Assign to Team"
      />
    )

    expect(screen.getByText('Assign to Team')).toBeInTheDocument()
  })
})

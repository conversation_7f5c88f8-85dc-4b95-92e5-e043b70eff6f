import React from 'react';
import { TextResult } from './types';
import { ExternalLinkIcon, FileIcon, GlobeIcon } from './icons';

interface SourcesDisplayProps {
  sources: TextResult[];
  style?: React.CSSProperties;
}

export const SourcesDisplay: React.FC<SourcesDisplayProps> = ({ sources, style }) => {
  if (!sources || sources.length === 0) {
    return null;
  }

  // Get file icon based on source or document type
  const getFileIcon = (metadata: TextResult['metadata']) => {
    const source = metadata?.source?.toLowerCase() || '';
    const docType = metadata?.document_type?.toLowerCase() || '';

    if (source.includes('.pdf') || docType.includes('pdf')) {
      return <FileIcon size={16} color="#dc3545" />;
    }
    if (
      source.includes('.doc') ||
      source.includes('.docx') ||
      docType.includes('doc')
    ) {
      return <FileIcon size={16} color="#007bff" />;
    }
    if (source.includes('http') || metadata?.url) {
      return <GlobeIcon size={16} color="#28a745" />;
    }
    return <FileIcon size={16} color="#6c757d" />;
  };

  const containerStyle: React.CSSProperties = {
    marginTop: '12px',
    padding: '8px',
    backgroundColor: '#f8f9fa',
    borderRadius: '6px',
    border: '1px solid #e9ecef',
    fontSize: '12px',
    ...style,
  };

  const headerStyle: React.CSSProperties = {
    fontWeight: '600',
    color: '#495057',
    marginBottom: '6px',
    fontSize: '11px',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  };

  const sourceItemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'flex-start',
    gap: '6px',
    marginBottom: '6px',
    padding: '4px',
    backgroundColor: '#ffffff',
    borderRadius: '4px',
    border: '1px solid #dee2e6',
  };

  const sourceContentStyle: React.CSSProperties = {
    flex: 1,
    minWidth: 0,
  };

  const sourceTitleStyle: React.CSSProperties = {
    fontWeight: '500',
    color: '#212529',
    marginBottom: '2px',
    fontSize: '11px',
    lineHeight: '1.3',
  };

  const sourceMetaStyle: React.CSSProperties = {
    color: '#6c757d',
    fontSize: '10px',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    flexWrap: 'wrap',
  };

  const linkStyle: React.CSSProperties = {
    color: '#007bff',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    gap: '2px',
    fontSize: '10px',
  };

  const scoreStyle: React.CSSProperties = {
    backgroundColor: '#e9ecef',
    color: '#495057',
    padding: '1px 4px',
    borderRadius: '2px',
    fontSize: '9px',
    fontWeight: '500',
  };

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>Sources</div>
      {sources.slice(0, 3).map((source, index) => (
        <div key={index} style={sourceItemStyle}>
          <div style={{ marginTop: '1px' }}>
            {getFileIcon(source.metadata)}
          </div>
          <div style={sourceContentStyle}>
            <div style={sourceTitleStyle}>
              {source.metadata?.title || 
               source.metadata?.source?.split('/').pop() || 
               `Document ${index + 1}`}
            </div>
            <div style={sourceMetaStyle}>
              <span style={scoreStyle}>
                {Math.round(source.score * 100)}% match
              </span>
              <span>{source.wordCount} words</span>
              {source.metadata?.url && (
                <a
                  href={source.metadata.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={linkStyle}
                  onMouseOver={(e) => {
                    e.currentTarget.style.textDecoration = 'underline';
                  }}
                  onMouseOut={(e) => {
                    e.currentTarget.style.textDecoration = 'none';
                  }}
                >
                  View source
                  <ExternalLinkIcon size={8} />
                </a>
              )}
            </div>
          </div>
        </div>
      ))}
      {sources.length > 3 && (
        <div style={{ 
          fontSize: '10px', 
          color: '#6c757d', 
          textAlign: 'center',
          marginTop: '4px',
          fontStyle: 'italic'
        }}>
          +{sources.length - 3} more sources
        </div>
      )}
    </div>
  );
};

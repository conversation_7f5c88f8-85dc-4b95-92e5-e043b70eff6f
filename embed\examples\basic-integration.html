<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Integration Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .hero {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .feature {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>Welcome to Our Website</h1>
            <p>This is a basic example of integrating the Halal Chat Widget</p>
        </div>

        <div class="feature">
            <h3>🤖 AI-Powered Assistance</h3>
            <p>Get instant answers to your Halal-related questions using our AI assistant.</p>
        </div>

        <div class="feature">
            <h3>🎤 Voice Support</h3>
            <p>Use voice input for hands-free interaction with the chat widget.</p>
        </div>

        <div class="feature">
            <h3>📷 Image Analysis</h3>
            <p>Upload images of products or ingredients for Halal verification.</p>
        </div>

        <div class="feature">
            <h3>🌐 Multi-language</h3>
            <p>Available in English and Bahasa Malaysia for better accessibility.</p>
        </div>

        <p style="text-align: center; margin-top: 40px; color: #666;">
            Look for the chat button in the bottom-right corner to get started!
        </p>
    </div>

    <!-- Halal Chat Widget Integration -->
    <script src="../dist/halal-chat-widget.js"></script>
    <script>
        // Basic configuration
        HalalChatWidget.init({
            baseURL: 'http://localhost:16001', // Replace with your API server URL
            position: 'bottom-right',
            theme: 'light',
            language: 'en'
        });
    </script>
</body>
</html>

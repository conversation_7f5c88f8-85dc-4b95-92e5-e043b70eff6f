import { create } from 'zustand'
import type { Team, TeamWithMembers, TeamFilterOptions } from '@/types/team'
import { useAuthStore } from './auth'

interface TeamsState {
  // State
  teams: Team[]
  currentTeam: TeamWithMembers | null
  isLoading: boolean
  error: string | null
  
  // Team filtering for agent dashboard
  teamFilter: TeamFilterOptions
}

interface TeamsActions {
  // Actions
  fetchTeams: () => Promise<void>
  fetchTeamById: (id: number) => Promise<TeamWithMembers | null>
  setTeamFilter: (filter: Partial<TeamFilterOptions>) => void
  clearError: () => void
  
  // Helper methods
  getTeamById: (id: number) => Team | null
  getTeamName: (id: number | null | undefined) => string | null
}

type TeamsStore = TeamsState & TeamsActions

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

export const useTeamsStore = create<TeamsStore>((set, get) => ({
  // Initial state
  teams: [],
  currentTeam: null,
  isLoading: false,
  error: null,
  teamFilter: {
    showAllTeams: true,
    selectedTeamId: null,
    availableTeams: [],
  },

  // Actions
  fetchTeams: async () => {
    set({ isLoading: true, error: null })
    try {
      const { agentToken, adminToken } = useAuthStore.getState()
      const token = agentToken || adminToken
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch teams: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch teams')
      }

      const teams = Array.isArray(result.data) ? result.data : result.data.items || []

      set({
        teams,
        teamFilter: {
          ...get().teamFilter,
          availableTeams: teams.filter(team => team.isActive),
        },
        isLoading: false,
      })
    } catch (error) {
      console.error('Error fetching teams:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch teams',
        isLoading: false,
      })
    }
  },

  fetchTeamById: async (id: number) => {
    set({ isLoading: true, error: null })
    try {
      const { agentToken } = useAuthStore.getState()
      if (!agentToken) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams/${id}`, {
        headers: {
          Authorization: `Bearer ${agentToken}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch team: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch team')
      }

      const team = result.data as TeamWithMembers
      set({ currentTeam: team, isLoading: false })
      return team
    } catch (error) {
      console.error('Error fetching team:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch team',
        isLoading: false,
      })
      return null
    }
  },

  setTeamFilter: (filter: Partial<TeamFilterOptions>) => {
    set({
      teamFilter: {
        ...get().teamFilter,
        ...filter,
      },
    })
  },

  clearError: () => {
    set({ error: null })
  },

  // Helper methods
  getTeamById: (id: number) => {
    const { teams } = get()
    return teams.find(team => team.id === id) || null
  },

  getTeamName: (id: number | null | undefined) => {
    if (!id) return null
    const team = get().getTeamById(id)
    return team?.name || null
  },

  // Team assignment operations
  assignUserToTeam: async (userId: number, teamId: number | null) => {
    try {
      const { agentToken, adminToken } = useAuthStore.getState()
      const token = agentToken || adminToken
      if (!token) {
        throw new Error('No authentication token')
      }

      if (teamId === null) {
        // Remove user from team
        const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}/team`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to remove user from team')
        }
      } else {
        // Assign user to team
        const response = await fetch(`${API_BASE_URL}/api/admin/users/${userId}/team`, {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ teamId }),
        })

        if (!response.ok) {
          throw new Error('Failed to assign user to team')
        }
      }

      return true
    } catch (error) {
      console.error('Error in team assignment:', error)
      throw error
    }
  },

  assignBotToTeam: async (botId: number, teamId: number | null) => {
    try {
      const { agentToken, adminToken } = useAuthStore.getState()
      const token = agentToken || adminToken
      if (!token) {
        throw new Error('No authentication token')
      }

      if (teamId === null) {
        // Remove bot from team
        const response = await fetch(`${API_BASE_URL}/api/admin/bots/${botId}/team`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error('Failed to remove bot from team')
        }
      } else {
        // Assign bot to team
        const response = await fetch(`${API_BASE_URL}/api/admin/bots/${botId}/team`, {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ teamId }),
        })

        if (!response.ok) {
          throw new Error('Failed to assign bot to team')
        }
      }

      return true
    } catch (error) {
      console.error('Error in bot team assignment:', error)
      throw error
    }
  },
}))

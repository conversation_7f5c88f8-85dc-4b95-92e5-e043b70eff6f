'use client'

import { useEffect } from 'react'
import { Select } from './select'
import { Label } from './label'
import { useTeamsStore } from '@/stores/teams'

interface TeamSelectProps {
  value?: number | null
  onChange: (teamId: number | null) => void
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

export function TeamSelect({
  value,
  onChange,
  label = 'Team',
  placeholder = 'Select a team',
  required = false,
  disabled = false,
  className,
}: TeamSelectProps) {
  const { teams, isLoading, fetchTeams } = useTeamsStore()

  useEffect(() => {
    // Fetch teams when component mounts
    if (teams.length === 0) {
      fetchTeams()
    }
  }, [teams.length, fetchTeams])

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value
    if (selectedValue === '') {
      onChange(null)
    } else {
      onChange(parseInt(selectedValue, 10))
    }
  }

  return (
    <div className="space-y-2">
      <Label htmlFor="team-select">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select
        id="team-select"
        value={value?.toString() || ''}
        onChange={handleChange}
        disabled={disabled || isLoading}
        className={className}
        required={required}
      >
        <option value="">{placeholder}</option>
        {teams
          .filter(team => team.isActive) // Only show active teams
          .map(team => (
            <option key={team.id} value={team.id.toString()}>
              {team.name}
            </option>
          ))}
      </Select>
      {isLoading && (
        <p className="text-sm text-muted-foreground">Loading teams...</p>
      )}
      {!isLoading && teams.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No active teams available. Create a team first.
        </p>
      )}
    </div>
  )
}

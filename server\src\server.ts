import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger as honoLogger } from 'hono/logger'
import { createBunWebSocket } from 'hono/bun'
import { v4 as uuidv4 } from 'uuid'
import type { ServerWebSocket } from 'bun'

// Import your existing services and handlers
import authService from './middleware/auth'
import adminRouter from './routes/admin'
import adminAgentsRouter from './routes/admin/agents'
import adminBotsRouter from './routes/admin/bots'
import adminCollectionsRouter from './routes/admin/collections'
import adminContactsRouter from './routes/admin/contacts'
import adminDocumentsRouter from './routes/admin/documents'
import adminTeamsRouter from './routes/admin/teams'
import healthRoutes from './routes/health'
import twilioRoutes from './routes/twilio'
import formAIRoutes from './routes/formAI'
import DatabaseService from './services/database'
import websocketService, { MESSAGE_TYPES } from './services/websocket'
import facebookService from './services/facebook'
import HalalKnowledgeService from './services/halalKnowledge'
import { createMessageHandlerService } from './services/messageHandler'
import openaiService from './services/openai'
import R2RService from './services/r2r'
import S3Service from './services/s3Service'
import whatsappService from './services/whatsapp'
import type {
  AgentLoginRequest,
  ChatMessage,
  ChatSession,
  FacebookWebhookPayload,
  HalalKnowledgeResponse,
  ParseR2rOptions,
  S3ConfigurationCreationRequest,
  S3ConfigurationPreset,
  S3ConfigurationUpdateRequest,
  SearchResponse,
} from './types'
import { UserRole } from './types'
import { logger } from './utils/logger'
import { AI_CONFIG } from './constants'

// Create Bun WebSocket
const { upgradeWebSocket, websocket } = createBunWebSocket()

const app = new Hono()

// Use process.env directly - no fallbacks
const env = process.env

// Initialize logger with environment
logger.initialize(env)
logger.info('Starting Halal Server', {
  nodeEnv: env.NODE_ENV,
  port: env.PORT || 3000,
  frontendUrl: env.FRONTEND_URL,
})

// Middleware
app.use('*', honoLogger())

// CORS configuration with debugging
const allowedOrigins = [
  process.env.FRONTEND_URL,
  'http://localhost:16000', // Main frontend
  'http://localhost:16005', // Alternative frontend
  'http://localhost:16010', // Selangor branch frontend
  'http://localhost:9000',  // Embed widget development server
].filter(Boolean) // Remove undefined values

console.log('🔧 CORS allowed origins:', allowedOrigins)

app.use(
  '*',
  cors({
    origin: allowedOrigins,
    credentials: true,
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }),
)

// Health routes
app.route('/health', healthRoutes)

// WebSocket handler functions
function handleRegistration(message: any, ws: any, connectionId: string) {
  const { data } = message

  if (data.connectionType === 'user' && data.sessionId) {
    websocketService.registerConnection(connectionId, 'user', {
      sessionId: data.sessionId,
    })
    logger.info('User connection registered', {
      connectionId,
      sessionId: data.sessionId,
    })
  } else if (data.connectionType === 'agent' && data.agentId) {
    websocketService.registerConnection(connectionId, 'agent', {
      agentId: data.agentId,
      sessionId: data.sessionId,
    })
    logger.info('Agent connection registered', {
      connectionId,
      agentId: data.agentId,
    })
  }

  // Send registration confirmation
  ws.send(
    JSON.stringify({
      type: 'registration_confirmed',
      data: { connectionType: data.connectionType },
    }),
  )
}

function handlePing(message: any, ws: any, connectionId: string) {
  websocketService.updateLastSeen(connectionId)

  ws.send(
    JSON.stringify({
      type: MESSAGE_TYPES.PONG,
      data: { timestamp: new Date().toISOString() },
    }),
  )
}

function handleAgentMessage(message: any, ws: any) {
  const { data } = message
  const { sessionId, content, agentId } = data

  if (!sessionId || !content) {
    logger.error('Invalid agent message data', { data })
    return
  }

  // Broadcast to all connections in the session
  websocketService.sendToSession(sessionId, {
    type: MESSAGE_TYPES.AGENT_MESSAGE,
    data: {
      sessionId,
      content,
      agentId,
      timestamp: new Date().toISOString(),
    },
  })

  logger.info('Agent message broadcasted', { sessionId, agentId })
}

function handleUserMessage(message: any, ws: any) {
  const { data } = message
  const { sessionId, content, userId } = data

  if (!sessionId || !content) {
    logger.error('Invalid user message data', { data })
    return
  }

  // Broadcast to agents monitoring this session
  websocketService.sendToSession(sessionId, {
    type: MESSAGE_TYPES.USER_MESSAGE,
    data: {
      sessionId,
      content,
      userId,
      timestamp: new Date().toISOString(),
    },
  })

  logger.info('User message broadcasted', { sessionId, userId })
}

// WebSocket route
app.get(
  '/ws',
  upgradeWebSocket((c) => {
    let connectionId: string

    return {
      onOpen(event, ws) {
        connectionId = websocketService.generateConnectionId()
        const clientInfo = {
          connectionId,
          userAgent: c.req.header('user-agent'),
          origin: c.req.header('origin'),
          host: c.req.header('host'),
          upgrade: c.req.header('upgrade'),
          connection: c.req.header('connection'),
          secWebSocketKey: c.req.header('sec-websocket-key'),
          secWebSocketVersion: c.req.header('sec-websocket-version'),
        }

        // logger.info('WebSocket connection opened', clientInfo);
        // console.log('🔌 WebSocket connection opened:', clientInfo);

        // Store the WebSocket connection in the service
        websocketService.addWebSocketConnection(connectionId, ws)

        // Send connection established message
        ws.send(
          JSON.stringify({
            type: MESSAGE_TYPES.CONNECTION_ESTABLISHED,
            data: { connectionId },
          }),
        )
      },
      onMessage(event, ws) {
        try {
          const message = JSON.parse(event.data.toString())
          logger.info('WebSocket message received', { message })

          // Handle different message types
          switch (message.type) {
            case 'register':
              handleRegistration(message, ws, connectionId)
              break
            case 'ping':
              handlePing(message, ws, connectionId)
              break
            case 'agent_message':
              handleAgentMessage(message, ws)
              break
            case 'user_message':
              handleUserMessage(message, ws)
              break
            default:
              logger.warn('Unknown WebSocket message type', {
                type: message.type,
              })
          }
        } catch (error) {
          logger.error('Error processing WebSocket message', { error })
          ws.send(
            JSON.stringify({
              type: 'error',
              data: { message: 'Invalid message format' },
            }),
          )
        }
      },
      onClose() {
        // logger.info('WebSocket connection closed', { connectionId });
        websocketService.removeConnection(connectionId)
      },
      onError(event, ws) {
        logger.error('WebSocket error', { connectionId, error: event })
      },
    }
  }),
)

// Import response utilities
import { createSuccessResponse } from './utils/response'

// Root route handler
app.get('/', (c) => {
  return createSuccessResponse(c, {
    message: 'Halal Server API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      chat: '/api/chat/*',
      admin: '/api/admin/*',
      whatsapp: '/api/whatsapp/*',
      facebook: '/api/facebook/*',
      upload: '/api/upload/*',
      search: '/api/search/*',
      'halal-knowledge': '/api/halal-knowledge/*',
      'form-ai': '/api/form-ai/*',
      websocket:
        'ws://localhost:' + (Number(process.env.PORT) || 16001) + '/ws',
    },
  })
})

// Store sessions in memory (same as original)
const whatsappSessions = new Map<string, ChatSession>()
const facebookSessions = new Map<string, ChatSession>()
const chatSessions = new Map<string, ChatSession>()

function getSiteId(path: string): string {
  const siteIdMatch = path.match(/^\/api\/sites\/(\d+)/)
  return siteIdMatch ? siteIdMatch[1] : process.env.DEFAULT_SITE_ID || '1'
}

// Copy all your handlers from index.ts
app.all('/api/whatsapp/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleWhatsAppRequest(request, env, dbService, siteId)
  return response
})

// Mount Twilio routes
app.route('/api/twilio', twilioRoutes)

// Mount Form AI routes
app.route('/api/form-ai', formAIRoutes)

app.all('/api/facebook/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleFacebookRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/chat/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleChatRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/upload/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleUploadRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/sites/:siteId/upload/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = c.req.param('siteId')

  const response = await handleUploadRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/admin/*', async (c) => {
  console.log('🚨 HONO ADMIN ROUTE HIT!', c.req.method, c.req.path)
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  console.log('🚨 Calling handleAdminRequest...')
  const response = await handleAdminRequest(request, env, dbService, siteId)
  console.log('🚨 handleAdminRequest completed')
  return response
})

// Handle site-prefixed admin routes
app.all('/api/sites/:siteId/admin/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = c.req.param('siteId')

  const response = await handleAdminRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/agent/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleAgentRequest(request, env, dbService, siteId)
  return response
})

// Handle site-prefixed agent routes
app.all('/api/sites/:siteId/agent/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = c.req.param('siteId')

  const response = await handleAgentRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/sessions/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleSessionRequest(request, env, dbService, siteId)
  return response
})

// Handle site-prefixed session routes
app.all('/api/sites/:siteId/sessions/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = c.req.param('siteId')

  const response = await handleSessionRequest(request, env, dbService, siteId)
  return response
})

app.all('/api/search/*', async (c) => {
  const request = c.req.raw
  const response = await handleSearchRequest(request, env)
  return response
})

app.all('/api/halal-knowledge/*', async (c) => {
  const request = c.req.raw
  const response = await handleHalalKnowledgeRequest(request, env)
  return response
})

app.all('/api/security/*', async (c) => {
  const request = c.req.raw
  const response = await handleSecurityRoutes(request, env)
  return response
})

app.all('/api/bots/*', async (c) => {
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = getSiteId(c.req.path)

  const response = await handleBotsRequest(request, env, dbService, siteId)
  return response
})

// Handle site-prefixed bots routes
app.all('/api/sites/:siteId/bots/*', async (c) => {
  console.log('🤖 Site-prefixed bots route hit:', c.req.method, c.req.path)
  const request = c.req.raw
  const dbService = new DatabaseService(env)
  const siteId = c.req.param('siteId')
  console.log('🤖 Site ID:', siteId)

  const response = await handleBotsRequest(request, env, dbService, siteId)
  return response
})

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Route not found' }, 404)
})

// Copy all handler functions from your index.ts (I'll include the key ones)

// WhatsApp route handler
async function handleWhatsAppRequest(
  request: Request,
  env: any,
  _dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Webhook verification endpoint
  if (path === '/api/whatsapp/webhook' && request.method === 'GET') {
    try {
      const mode = url.searchParams.get('hub.mode')
      const token = url.searchParams.get('hub.verify_token')
      const challenge = url.searchParams.get('hub.challenge')

      console.log('Webhook verification request:', { mode, token, challenge })

      const verificationResult = whatsappService.verifyWebhook(
        mode,
        token,
        challenge,
      )

      if (verificationResult) {
        console.log('Webhook verified successfully')
        return new Response(verificationResult, {
          headers: { 'Content-Type': 'text/plain' },
          status: 200,
        })
      }
      console.log('Webhook verification failed')
      return new Response('Forbidden', {
        headers: { 'Content-Type': 'text/plain' },
        status: 403,
      })
    } catch (error) {
      console.error('Webhook verification error:', error)
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      })
    }
  }

  // Webhook message endpoint
  if (path === '/api/whatsapp/webhook' && request.method === 'POST') {
    try {
      const body = await request.json()
      console.log('Received WhatsApp webhook:', JSON.stringify(body, null, 2))

      // Process webhook payload
      const messages = await whatsappService.processWebhookMessage(body as any)

      if (messages && messages.length > 0) {
        // Handle each incoming message
        for (const message of messages) {
          await handleIncomingMessage(message, env)
        }
      }

      return new Response('OK', {
        headers: { 'Content-Type': 'text/plain' },
        status: 200,
      })
    } catch (error) {
      console.error('Webhook processing error:', error)
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      })
    }
  }

  // Default response for unknown WhatsApp routes
  return new Response(JSON.stringify({ error: 'WhatsApp route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

// Handle incoming WhatsApp message using consolidated handler
async function handleIncomingMessage(message: any, env: any): Promise<void> {
  try {
    const phoneNumber = message.from
    const messageText = message.content

    // Use consolidated message handler
    const response = await createMessageHandlerService().handleIncomingMessage(
      {
        message: messageText,
        sessionId: phoneNumber, // Use phone number as session ID for WhatsApp
        platform: 'whatsapp',
        messageType: message.type === 'image' ? 'image' : 'text',
        mediaUrl: message.mediaUrl,
        userId: phoneNumber,
        config: {
          maxMessageHistory: 10,
          enableToolCalling: true,
          defaultModel: AI_CONFIG.DEFAULT_MODEL,
        },
      },
      env,
    )

    if (response.success && response.message) {
      // Send response back to WhatsApp
      const sendResult = await whatsappService.sendTextMessage(
        phoneNumber,
        response.message,
      )

      if (!sendResult.success) {
        console.error('Failed to send WhatsApp response:', sendResult.error)
      } else {
        console.log(`Sent response to ${phoneNumber}: ${response.message}`)
      }
    } else {
      console.error('Failed to get AI response:', response.error)
    }
  } catch (error) {
    console.error('Error handling incoming message:', error)
  }
}

// Copy all other handlers from index.ts - I'll add placeholders for brevity
async function handleFacebookRequest(
  request: Request,
  env: any,
  dbService: DatabaseService,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Initialize Facebook service with database service for this request
  facebookService.initialize(dbService, Number.parseInt(siteId, 10))

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  // Webhook verification endpoint
  if (path === '/api/facebook/webhook' && request.method === 'GET') {
    try {
      const mode = url.searchParams.get('hub.mode')
      const token = url.searchParams.get('hub.verify_token')
      const challenge = url.searchParams.get('hub.challenge')

      console.log('Facebook webhook verification request:', {
        mode,
        token,
        challenge,
      })

      const verificationResult = facebookService.verifyWebhookChallenge(
        mode || '',
        token || '',
        challenge || '',
      )

      if (verificationResult) {
        console.log('Facebook webhook verified successfully')
        return new Response(verificationResult, {
          headers: { 'Content-Type': 'text/plain' },
          status: 200,
        })
      }
      console.log('Facebook webhook verification failed')
      return new Response('Forbidden', {
        headers: { 'Content-Type': 'text/plain' },
        status: 403,
      })
    } catch (error) {
      console.error('Facebook webhook verification error:', error)
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      })
    }
  }

  // Webhook message endpoint
  if (path === '/api/facebook/webhook' && request.method === 'POST') {
    try {
      const signature = request.headers.get('x-hub-signature-256') || ''
      const body = await request.text()

      // Verify webhook signature for security
      if (!facebookService.verifyWebhookSignature(body, signature)) {
        console.log('Facebook webhook signature verification failed')
        return new Response('Forbidden', {
          headers: { 'Content-Type': 'text/plain' },
          status: 403,
        })
      }

      const webhookPayload: FacebookWebhookPayload = JSON.parse(body)
      console.log(
        'Received Facebook webhook:',
        JSON.stringify(webhookPayload, null, 2),
      )

      // Process each entry in the webhook payload
      for (const entry of webhookPayload.entry) {
        for (const messagingEvent of entry.messaging) {
          if (messagingEvent.message && !messagingEvent.message.is_echo) {
            // Handle incoming message
            await handleIncomingFacebookMessage(messagingEvent, env)
          } else if (messagingEvent.postback) {
            // Handle postback (button clicks, etc.)
            await handleFacebookPostback(messagingEvent, env)
          }
        }
      }

      return new Response('OK', {
        headers: { 'Content-Type': 'text/plain' },
        status: 200,
      })
    } catch (error) {
      console.error('Facebook webhook processing error:', error)
      return new Response('Internal Server Error', {
        headers: { 'Content-Type': 'text/plain' },
        status: 500,
      })
    }
  }

  // Default response for unknown Facebook routes
  return new Response(JSON.stringify({ error: 'Facebook route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

// Handle incoming Facebook message using consolidated handler
async function handleIncomingFacebookMessage(
  messagingEvent: any,
  env: any,
): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const message = messagingEvent.message

    let messageText = ''
    let messageType = 'text'
    let mediaUrl: string | undefined

    if (message.text) {
      messageText = message.text
      messageType = 'text'
    } else if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0]
      messageType = attachment.type
      mediaUrl = attachment.payload.url
      messageText = `[${attachment.type.toUpperCase()} received]`
    } else if (message.quick_reply) {
      messageText = message.quick_reply.payload
      messageType = 'quick_reply'
    }

    // Use consolidated message handler
    const response = await createMessageHandlerService().handleIncomingMessage(
      {
        message: messageText,
        sessionId: senderId, // Use sender ID as session ID for Facebook
        platform: 'facebook',
        messageType: messageType === 'image' ? 'image' : 'text',
        mediaUrl,
        userId: senderId,
        config: {
          maxMessageHistory: 10,
          enableToolCalling: true,
          defaultModel: AI_CONFIG.DEFAULT_MODEL,
        },
      },
      env,
    )

    if (response.success && response.message) {
      // Send response back to Facebook Messenger
      const sendResult = await facebookService.sendTextMessage(
        senderId,
        response.message,
      )

      if (!sendResult.success) {
        console.error('Failed to send Facebook message:', sendResult.error)
      } else {
        console.log(
          `Sent Facebook response to ${senderId}: ${response.message}`,
        )
      }
    } else {
      console.error('AI request failed:', response.error)

      // Send error message to user
      await facebookService.sendTextMessage(
        senderId,
        'Sorry, I encountered an error processing your message. Please try again later.',
      )
    }
  } catch (error) {
    console.error('Error handling Facebook message:', error)
  }
}

// Handle Facebook postback events (button clicks, etc.)
async function handleFacebookPostback(
  messagingEvent: any,
  env: any,
): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const postback = messagingEvent.postback

    console.log(
      `Processing Facebook postback from ${senderId}: ${postback.payload}`,
    )

    // Handle different postback payloads
    switch (postback.payload) {
      case 'GET_STARTED':
        await facebookService.sendTextMessage(
          senderId,
          'Welcome to Halal Malaysia! I can help you with halal certification inquiries. How can I assist you today?',
        )
        break

      case 'HELP':
        await facebookService.sendTextMessage(
          senderId,
          'I can help you with:\n• Halal certification information\n• Application procedures\n• Requirements and guidelines\n• General halal inquiries\n\nJust ask me anything!',
        )
        break

      default:
        // Treat unknown postbacks as regular messages
        await handleIncomingFacebookMessage(
          {
            sender: { id: senderId },
            message: { text: postback.payload },
          },
          env,
        )
        break
    }
  } catch (error) {
    console.error('Error handling Facebook postback:', error)
  }
}

async function handleChatRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  // Create a new chat session
  if (normalizedPath === '/api/chat/session' && request.method === 'POST') {
    const sessionId = uuidv4()
    chatSessions.set(sessionId, {
      id: sessionId,
      messages: [],
      createdAt: new Date(),
    })

    return new Response(JSON.stringify({ sessionId }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  }

  // Send a text message
  if (path === '/api/chat/message' && request.method === 'POST') {
    try {
      const body = (await request.json()) as any
      const { sessionId, message, model, botSlug } = body

      if (!sessionId || !message) {
        return new Response(
          JSON.stringify({ error: 'sessionId and message are required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Get or create session
      let session = chatSessions.get(sessionId)
      if (!session) {
        session = {
          id: sessionId,
          messages: [
            {
              role: 'system',
              content:
                'You are a helpful assistant. You speak in Bahasa Malaysia by default. You may switch between Bahasa Malaysia, Chinese, English, and Tamil.',
            },
          ],
          createdAt: new Date(),
        }
        chatSessions.set(sessionId, session)

        // Create session in database
        await dbService.createChatSession(sessionId, 'web')
      }

      // Use consolidated message handler
      const response =
        await createMessageHandlerService().handleIncomingMessage(
          {
            message,
            sessionId,
            platform: 'web',
            messageType: 'text',
            userId: 'web', // For web chat, we use a generic user ID
            botSlug,
            config: {
              maxMessageHistory: 10,
              enableToolCalling: true,
              defaultModel: model || AI_CONFIG.DEFAULT_MODEL,
            },
          },
          env,
        )

      if (!response.success) {
        return new Response(
          JSON.stringify({ error: response.error || 'AI request failed' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      return new Response(
        JSON.stringify({
          message: response.message,
          sessionId,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Chat message error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }
  }

  // Analyze image with optional text prompt
  if (path === '/api/chat/image' && request.method === 'POST') {
    try {
      const body = (await request.json()) as any
      const { sessionId, imageUrl, prompt, model, botSlug } = body

      if (!sessionId || !imageUrl) {
        return new Response(
          JSON.stringify({ error: 'sessionId and imageUrl are required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Use consolidated message handler for image analysis
      const response =
        await createMessageHandlerService().handleIncomingMessage(
          {
            message: prompt || "What's in this image?",
            sessionId,
            platform: 'web',
            messageType: 'image',
            mediaUrl: imageUrl,
            botSlug,
            userId: 'web',
            config: {
              maxMessageHistory: 10,
              enableToolCalling: true,
              defaultModel: model || AI_CONFIG.DEFAULT_MODEL,
            },
          },
          env,
        )

      if (!response.success) {
        return new Response(
          JSON.stringify({ error: response.error || 'Image analysis failed' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      return new Response(
        JSON.stringify({
          message: response.message,
          sessionId,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Image analysis error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }
  }

  // Handle tool results from frontend
  if (path === '/api/chat/tool-results' && request.method === 'POST') {
    try {
      const body = (await request.json()) as any
      const { sessionId, toolResults, botSlug } = body

      if (!sessionId || !toolResults) {
        return new Response(
          JSON.stringify({ error: 'sessionId and toolResults are required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Handle tool results and continue conversation
      const response = await createMessageHandlerService().handleToolResults(
        sessionId,
        toolResults,
        env
      )

      if (!response.success) {
        return new Response(
          JSON.stringify({ error: response.error || 'Failed to process tool results' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      return new Response(
        JSON.stringify({
          message: response.message || '',
          answer: response.answer,
          sources: response.sources,
          sessionId,
          usage: response.usage,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Tool results error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }
  }

  // Get WhatsApp integration status
  if (path === '/api/chat/whatsapp-status' && request.method === 'GET') {
    try {
      const isConfigured = whatsappService.isConfigured()
      const config = whatsappService.getConfig()

      return new Response(
        JSON.stringify({
          whatsappEnabled: isConfigured,
          phoneNumber: config?.phoneNumberId
            ? `+${config.phoneNumberId}`
            : null,
          message: isConfigured
            ? 'WhatsApp integration is active. You can also message us on WhatsApp!'
            : 'WhatsApp integration is not configured.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error getting WhatsApp status:', error)
      return new Response(
        JSON.stringify({
          whatsappEnabled: false,
          phoneNumber: null,
          message: 'WhatsApp integration is not available.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }
  }

  // Get Facebook integration status
  if (path === '/api/chat/facebook-status' && request.method === 'GET') {
    try {
      const isConfigured = facebookService.isConfigured()
      const config = facebookService.getConfig()

      return new Response(
        JSON.stringify({
          facebookEnabled: isConfigured,
          pageId: config?.pageId || null,
          message: isConfigured
            ? 'Facebook Messenger integration is active. You can also message us on Facebook!'
            : 'Facebook Messenger integration is not configured.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error getting Facebook status:', error)
      return new Response(
        JSON.stringify({
          facebookEnabled: false,
          pageId: null,
          message: 'Facebook Messenger integration is not available.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }
  }

  // Default response for unknown chat routes
  return new Response(JSON.stringify({ error: 'Chat route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

async function handleUploadRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  // Handle file upload
  if (normalizedPath === '/api/upload' && request.method === 'POST') {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File

      if (!file) {
        return new Response(JSON.stringify({ error: 'No file uploaded' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      // Check file size (10MB limit)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        return new Response(
          JSON.stringify({ error: 'File too large. Maximum size is 10MB.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Validate file types
      const allowedImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
      ]
      const allowedAudioTypes = [
        'audio/mpeg',
        'audio/wav',
        'audio/webm',
        'audio/ogg',
        'video/webm', // WebM container can contain audio-only content from MediaRecorder
      ]

      if (
        !allowedImageTypes.includes(file.type) &&
        !allowedAudioTypes.includes(file.type)
      ) {
        console.error('Invalid file type:', file.type)
        return new Response(
          JSON.stringify({
            error:
              'Invalid file type. Only images (JPEG, PNG, GIF, WebP) and audio files (MP3, WAV, WebM, OGG) are allowed.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Get default S3 configuration for the site
      const siteIdNum = Number.parseInt(siteId, 10)
      if (Number.isNaN(siteIdNum)) {
        return new Response(JSON.stringify({ error: 'Invalid site ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const s3Config = await dbService.getS3ConfigurationBySiteId(siteIdNum)
      if (!s3Config) {
        return new Response(
          JSON.stringify({ error: 'No S3 configuration found for this site' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      // Initialize S3 service
      const s3Service = new S3Service(s3Config)

      // Generate unique filename
      const fileExtension = file.name.split('.').pop() || ''
      const s3Key = `uploads/${siteId}/${uuidv4()}.${fileExtension}`

      // Convert file to buffer
      const fileBuffer = Buffer.from(await file.arrayBuffer())

      // Upload to S3
      const uploadResult = await s3Service.uploadFile(
        fileBuffer,
        s3Key,
        file.type,
      )

      // Check if it's an audio file for transcription
      if (allowedAudioTypes.includes(file.type)) {
        try {
          // Transcribe audio file using OpenAI Whisper
          console.log('Transcribing audio file:', file.name)

          // Create a File object for OpenAI transcription
          const audioFile = new File([fileBuffer], file.name, {
            type: file.type,
          })

          const transcriptionResponse = await openaiService.transcribeAudio(
            audioFile,
            env,
          )

          if (!transcriptionResponse.success) {
            console.error(
              'Audio transcription failed:',
              transcriptionResponse.error,
            )
            return new Response(
              JSON.stringify({
                type: 'audio',
                url: uploadResult.Location,
                s3Key: uploadResult.Key,
                originalFilename: file.name,
                size: file.size,
                mimetype: file.type,
                error: 'Transcription failed',
                transcriptionError: transcriptionResponse.error,
              }),
              {
                headers: { 'Content-Type': 'application/json' },
                status: 200, // Still return 200 since upload succeeded
              },
            )
          }

          console.log(
            'Audio transcribed successfully:',
            transcriptionResponse.text,
          )

          return new Response(
            JSON.stringify({
              type: 'audio',
              url: uploadResult.Location,
              s3Key: uploadResult.Key,
              originalFilename: file.name,
              size: file.size,
              mimetype: file.type,
              transcription: transcriptionResponse.text || '',
              message: 'Audio file uploaded and transcribed successfully.',
            }),
            {
              headers: { 'Content-Type': 'application/json' },
              status: 200,
            },
          )
        } catch (transcriptionError) {
          console.error('Error during transcription:', transcriptionError)
          return new Response(
            JSON.stringify({
              type: 'audio',
              url: uploadResult.Location,
              s3Key: uploadResult.Key,
              originalFilename: file.name,
              size: file.size,
              mimetype: file.type,
              error: 'Transcription failed',
              transcriptionError:
                transcriptionError instanceof Error
                  ? transcriptionError.message
                  : 'Unknown transcription error',
            }),
            {
              headers: { 'Content-Type': 'application/json' },
              status: 200, // Still return 200 since upload succeeded
            },
          )
        }
      } else {
        // Return image file info
        return new Response(
          JSON.stringify({
            type: 'image',
            url: uploadResult.Location,
            s3Key: uploadResult.Key,
            originalFilename: file.name,
            size: file.size,
            mimetype: file.type,
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
    } catch (error) {
      console.error('Upload processing error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }
  }

  // Handle file deletion
  if (
    normalizedPath.startsWith('/api/upload/') &&
    request.method === 'DELETE'
  ) {
    try {
      const s3Key = normalizedPath.replace('/api/upload/', '')
      if (!s3Key) {
        return new Response(JSON.stringify({ error: 'S3 key is required' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      // Get default S3 configuration for the site
      const siteIdNum = Number.parseInt(siteId, 10)
      if (Number.isNaN(siteIdNum)) {
        return new Response(JSON.stringify({ error: 'Invalid site ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const s3Config = await dbService.getS3ConfigurationBySiteId(siteIdNum)
      if (!s3Config) {
        return new Response(
          JSON.stringify({ error: 'No S3 configuration found for this site' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      // Initialize S3 service and delete file
      const s3Service = new S3Service(s3Config)
      await s3Service.deleteFile(s3Key)

      return new Response(
        JSON.stringify({ message: 'File deleted successfully' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('File deletion error:', error)
      return new Response(JSON.stringify({ error: 'Internal server error' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }
  }

  // Default response for unknown upload routes
  return new Response(JSON.stringify({ error: 'Upload route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

async function handleAdminRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Handle OPTIONS requests (CORS preflight) - consistent with Hono CORS middleware
  if (request.method === 'OPTIONS') {
    const origin = request.headers.get('Origin')
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      'http://localhost:16000',
      'http://localhost:16005',
      'http://localhost:16010',
      'http://localhost:9000',  // Embed widget development server
    ].filter(Boolean) // Remove undefined values

    console.log('🔧 OPTIONS request from origin:', origin)
    console.log('🔧 Allowed origins:', allowedOrigins)

    const allowOrigin = allowedOrigins.includes(origin) ? origin : allowedOrigins[0] || 'http://localhost:16000'

    console.log('🔧 Responding with Allow-Origin:', allowOrigin)

    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': allowOrigin,
        'Access-Control-Allow-Methods':
          'GET, POST, PUT, DELETE, OPTIONS, PATCH',
        'Access-Control-Allow-Headers':
          'Content-Type, Authorization, X-Requested-With',
        'Access-Control-Allow-Credentials': 'true',
      },
    })
  }

  console.log('🔍 Admin request:', request.method, path)
  console.log('🔍 Database URL:', process.env.DATABASE_URL)
  console.log('🔍 Database service type:', typeof dbService)
  console.log('🔍 Database service constructor:', dbService.constructor.name)

  // Initialize Facebook service with database service for admin requests
  console.log('🔧 Initializing Facebook service with database service...')
  facebookService.initialize(dbService, Number.parseInt(siteId, 10))
  console.log('🔧 Facebook service initialized')

  console.log('🔧 Admin request:', request.method, path)

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  console.log('🔧 Normalized path:', normalizedPath)

  // Handle admin login
  if (normalizedPath === '/api/admin/login' && request.method === 'POST') {
    try {
      const body = (await request.json()) as {
        username: string
        password: string
        siteId?: string
      }
      const { username, password } = body

      if (!username || !password) {
        return new Response(
          JSON.stringify({
            error: 'Validation error',
            message: 'Username and password are required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Use the existing auth service for login with siteId
      try {
        const result = await authService.login(
          { username, password },
          dbService,
          env,
          siteId,
        )

        if (result.success) {
          return new Response(JSON.stringify(result), {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          })
        }
        return new Response(JSON.stringify(result), {
          headers: { 'Content-Type': 'application/json' },
          status: 401,
        })
      } catch (error: any) {
        console.error('Database login error:', error)

        // Fallback for testing when database is not available
        if (username === 'admin' && password === 'admin123') {
          const mockToken = authService.generateToken(
            1, // userId
            'admin', // username
            UserRole.ADMIN, // role
          )

          return new Response(
            JSON.stringify({
              success: true,
              token: mockToken,
              user: {
                id: 1,
                username: 'admin',
                role: 'ADMIN',
              },
            }),
            {
              headers: { 'Content-Type': 'application/json' },
              status: 200,
            },
          )
        }

        return new Response(
          JSON.stringify({
            success: false,
            error: 'Invalid credentials',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }
    } catch (error) {
      console.error('Login error:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Login failed',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get current admin user info
  if (normalizedPath === '/api/admin/me' && request.method === 'GET') {
    try {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'No token provided',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const token = authHeader.substring(7)
      const decoded = authService.verifyToken(token)
      if (!decoded) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'Invalid token',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      return new Response(
        JSON.stringify({
          user: {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error getting user info:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get user info',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Verify admin token
  if (normalizedPath === '/api/admin/verify' && request.method === 'GET') {
    try {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'No token provided',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const token = authHeader.substring(7)
      const decoded = authService.verifyToken(token)
      if (!decoded) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'Invalid token',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          user: {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error verifying admin token:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to verify token',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get WhatsApp configuration
  if (
    normalizedPath === '/api/admin/whatsapp/config' &&
    request.method === 'GET'
  ) {
    try {
      const config = whatsappService.getConfig()
      return new Response(
        JSON.stringify({
          configured: whatsappService.isConfigured(),
          config: config
            ? {
                phoneNumberId: config.phoneNumberId,
                businessAccountId: config.businessAccountId,
                isActive: config.isActive,
              }
            : null,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error getting WhatsApp config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get WhatsApp configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Save WhatsApp configuration
  if (
    normalizedPath === '/api/admin/whatsapp/config' &&
    request.method === 'POST'
  ) {
    try {
      const body = (await request.json()) as any
      const {
        accessToken,
        phoneNumberId,
        webhookVerifyToken,
        businessAccountId,
      } = body

      if (!accessToken || !phoneNumberId || !webhookVerifyToken) {
        return new Response(
          JSON.stringify({
            error: 'Validation error',
            message:
              'Access token, phone number ID, and webhook verify token are required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const config = {
        accessToken,
        phoneNumberId,
        webhookVerifyToken,
        businessAccountId: businessAccountId || undefined,
        isActive: true,
      }

      await whatsappService.updateConfig(config)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'WhatsApp configuration saved successfully',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error saving WhatsApp config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to save WhatsApp configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Test WhatsApp configuration
  if (
    normalizedPath === '/api/admin/whatsapp/test' &&
    request.method === 'POST'
  ) {
    try {
      const result = await whatsappService.testConfiguration()

      if (result.success) {
        return new Response(
          JSON.stringify({
            success: true,
            message: 'WhatsApp configuration is valid',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    } catch (error) {
      console.error('Error testing WhatsApp config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to test WhatsApp configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Send test WhatsApp message
  if (
    normalizedPath === '/api/admin/whatsapp/test-message' &&
    request.method === 'POST'
  ) {
    try {
      const body = (await request.json()) as any
      const { to, message, type = 'text', imageUrl } = body

      if (
        !to ||
        !to.trim() ||
        (type === 'text' && (!message || !message.trim())) ||
        (type === 'image' && (!imageUrl || !imageUrl.trim()))
      ) {
        return new Response(
          JSON.stringify({
            error: 'Validation error',
            message: 'Invalid request parameters',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      let result
      if (type === 'image') {
        result = await whatsappService.sendImageMessage(to, imageUrl, message)
      } else {
        result = await whatsappService.sendTextMessage(to, message)
      }

      if (result.success) {
        return new Response(
          JSON.stringify({
            success: true,
            messageId: result.messageId,
            message: 'Test message sent successfully',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    } catch (error) {
      console.error('Error sending test message:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to send test message',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get webhook URL for WhatsApp configuration
  if (
    normalizedPath === '/api/admin/whatsapp/webhook-url' &&
    request.method === 'GET'
  ) {
    const baseUrl = process.env.WEBHOOK_BASE_URL
    const webhookUrl = `${baseUrl}/api/whatsapp/webhook`

    return new Response(
      JSON.stringify({
        webhookUrl,
        instructions: [
          '1. Copy the webhook URL above',
          '2. Go to your WhatsApp Business API configuration',
          '3. Set the webhook URL to the URL above',
          '4. Set the verify token to match your configuration',
          '5. Subscribe to "messages" events',
        ],
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  }

  // Test endpoint to verify logging
  if (
    normalizedPath === '/api/admin/test-logging' &&
    request.method === 'GET'
  ) {
    console.log('🧪 TEST LOGGING ENDPOINT HIT!')
    console.log('🧪 This should show up in server logs')
    logger.info('🧪 LOGGER TEST: Endpoint hit!', {
      endpoint: '/api/admin/test-logging',
    })
    logger.debug('🧪 LOGGER DEBUG: This is a debug message')
    logger.warn('🧪 LOGGER WARN: This is a warning message')
    logger.error('🧪 LOGGER ERROR: This is an error message')
    return new Response(
      JSON.stringify({
        message: 'Test logging endpoint hit',
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  }

  // Facebook Admin Routes

  // Get all Facebook configurations
  if (
    normalizedPath === '/api/admin/facebook-configs' &&
    request.method === 'GET'
  ) {
    console.log('🔍 Facebook configs endpoint hit!')
    try {
      const configs = await dbService.getAllFacebookConfigs()
      console.log('📘 Found configs:', configs.length)
      return new Response(JSON.stringify(configs), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error) {
      console.error('Error getting Facebook configs:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get Facebook configurations',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get Facebook configuration
  if (
    normalizedPath === '/api/admin/facebook/config' &&
    request.method === 'GET'
  ) {
    try {
      // Initialize Facebook service with database service
      facebookService.initialize(dbService, 1)

      const config = facebookService.getConfig()
      return new Response(
        JSON.stringify({
          configured: facebookService.isConfigured(),
          config: config
            ? {
                pageId: config.pageId,
                isActive: config.isActive,
              }
            : null,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error getting Facebook config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get Facebook configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Save Facebook configuration
  if (
    normalizedPath === '/api/admin/facebook/config' &&
    request.method === 'POST'
  ) {
    console.log('🔄 Facebook config save endpoint hit!')
    try {
      // Initialize Facebook service with database service
      console.log('🔧 Initializing Facebook service with database service...')
      facebookService.initialize(dbService, 1)
      console.log('🔧 Facebook service initialized')

      const body = (await request.json()) as any
      const { pageAccessToken, pageId, appSecret, verifyToken } = body

      console.log('📝 Received config data:', { pageId })

      if (!pageAccessToken || !pageId || !appSecret || !verifyToken) {
        console.log('❌ Validation failed - missing required fields')
        return new Response(
          JSON.stringify({
            error: 'Validation error',
            message:
              'Page access token, page ID, app secret, and verify token are required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const config = {
        pageAccessToken,
        pageId,
        appSecret,
        verifyToken,
        isActive: true,
      }

      console.log('💾 Calling facebookService.updateConfig with:', {
        pageId: config.pageId,
      })
      await facebookService.updateConfig(config)
      console.log('✅ facebookService.updateConfig completed')

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Facebook configuration saved successfully',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Error saving Facebook config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to save Facebook configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Test Facebook configuration
  if (
    normalizedPath === '/api/admin/facebook/test' &&
    request.method === 'POST'
  ) {
    try {
      const result = await facebookService.testConfiguration()

      if (result.success) {
        return new Response(
          JSON.stringify({
            success: true,
            message: 'Facebook configuration is valid',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    } catch (error) {
      console.error('Error testing Facebook config:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to test Facebook configuration',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get recent Facebook conversations
  if (
    normalizedPath === '/api/admin/facebook/conversations' &&
    request.method === 'GET'
  ) {
    try {
      const result = await facebookService.getRecentConversations()

      if (result.success) {
        return new Response(
          JSON.stringify({
            success: true,
            conversations: result.conversations,
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    } catch (error) {
      console.error('Error getting Facebook conversations:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to get conversations',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Send test Facebook message
  if (
    normalizedPath === '/api/admin/facebook/test-message' &&
    request.method === 'POST'
  ) {
    try {
      const body = (await request.json()) as any
      const { to, message, type = 'text', imageUrl } = body

      if (
        !to ||
        !to.trim() ||
        (type === 'text' && (!message || !message.trim())) ||
        (type === 'image' && (!imageUrl || !imageUrl.trim()))
      ) {
        return new Response(
          JSON.stringify({
            error: 'Validation error',
            message: 'Invalid request parameters',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      let result
      if (type === 'image') {
        result = await facebookService.sendImageMessage(to, imageUrl, message)
      } else {
        result = await facebookService.sendTextMessage(to, message)
      }

      if (result.success) {
        return new Response(
          JSON.stringify({
            success: true,
            messageId: result.messageId,
            message: 'Test message sent successfully',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    } catch (error) {
      console.error('Error sending test message:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to send test message',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get webhook URL for Facebook configuration
  if (
    normalizedPath === '/api/admin/facebook/webhook-url' &&
    request.method === 'GET'
  ) {
    const baseUrl = process.env.WEBHOOK_BASE_URL
    const webhookUrl = `${baseUrl}/api/facebook/webhook`

    return new Response(
      JSON.stringify({
        webhookUrl,
        instructions: [
          '1. Copy the webhook URL above',
          '2. Go to your Facebook App configuration in Meta for Developers',
          '3. Set the webhook URL to the URL above',
          '4. Set the verify token to match your configuration',
          '5. Subscribe to "messages", "messaging_postbacks", and "messaging_optins" events',
        ],
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  }

  // User Management Routes

  // Get all admin users
  if (normalizedPath === '/api/admin/users' && request.method === 'GET') {
    try {
      // Authentication check
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'No token provided',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const token = authHeader.substring(7)
      const decoded = authService.verifyToken(token)
      if (!decoded) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'Invalid token',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const users = await dbService.getAllAdminUsers()
      return new Response(JSON.stringify(users), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching admin users:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch admin users',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Create admin user
  if (normalizedPath === '/api/admin/users' && request.method === 'POST') {
    try {
      // Authentication check
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'No token provided',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const token = authHeader.substring(7)
      const decoded = authService.verifyToken(token)
      if (!decoded) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'Invalid token',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const body = (await request.json()) as any
      const {
        username,
        password,
        roles,
        email,
        firstName,
        lastName,
        isActive,
      } = body

      if (!username || !password || !roles || roles.length === 0) {
        return new Response(
          JSON.stringify({
            error: 'Username, password, and at least one role are required.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const newUser = await dbService.createAdminUser({
        username,
        password,
        roles,
        email,
        firstName,
        lastName,
        isActive,
      })

      return new Response(JSON.stringify(newUser), {
        headers: { 'Content-Type': 'application/json' },
        status: 201,
      })
    } catch (error: any) {
      console.error('Error creating admin user:', error)
      if (error.message?.includes('Username already exists')) {
        return new Response(JSON.stringify({ error: error.message }), {
          headers: { 'Content-Type': 'application/json' },
          status: 409,
        })
      }
      return new Response(
        JSON.stringify({
          error: 'Failed to create admin user',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get admin user by ID
  if (
    normalizedPath.match(/^\/api\/admin\/users\/\d+$/) &&
    request.method === 'GET'
  ) {
    try {
      // Authentication check
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'No token provided',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const token = authHeader.substring(7)
      const decoded = authService.verifyToken(token)
      if (!decoded) {
        return new Response(
          JSON.stringify({
            error: 'Access denied',
            message: 'Invalid token',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          }
        )
      }

      const userId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(userId)) {
        return new Response(JSON.stringify({ error: 'Invalid user ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const user = await dbService.getAdminUserById(userId)
      if (user) {
        return new Response(JSON.stringify(user), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      return new Response(JSON.stringify({ error: 'Admin user not found.' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 404,
      })
    } catch (error: any) {
      console.error('Error fetching admin user:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch admin user',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update admin user
  if (
    normalizedPath.match(/^\/api\/admin\/users\/\d+$/) &&
    request.method === 'PUT'
  ) {
    try {
      // TODO: Add proper authentication check
      const userId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(userId)) {
        return new Response(JSON.stringify({ error: 'Invalid user ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const body = (await request.json()) as any
      const {
        username,
        password,
        roles,
        email,
        firstName,
        lastName,
        isActive,
      } = body

      if (
        !username &&
        !password &&
        !roles &&
        !email &&
        !firstName &&
        !lastName &&
        isActive === undefined
      ) {
        return new Response(
          JSON.stringify({
            error: 'No update data provided for user.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const updatedUser = await dbService.updateAdminUser(userId, {
        username,
        password,
        roles,
        email,
        firstName,
        lastName,
        isActive,
      })

      if (updatedUser) {
        return new Response(JSON.stringify(updatedUser), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      return new Response(
        JSON.stringify({ error: 'Admin user not found or update failed.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error('Error updating admin user:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to update admin user',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete admin user
  if (
    normalizedPath.match(/^\/api\/admin\/users\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check
      const userId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(userId)) {
        return new Response(JSON.stringify({ error: 'Invalid user ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const deletedUser = await dbService.deleteAdminUser(userId)
      if (deletedUser) {
        return new Response(
          JSON.stringify({ message: 'Admin user deleted successfully.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }

      return new Response(
        JSON.stringify({ error: 'Admin user not found or delete failed.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error('Error deleting admin user:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to delete admin user',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Collections Routes

  // Get all collections
  if (normalizedPath === '/api/admin/collections' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check
      const collections = await dbService.getAllCollections()
      return new Response(JSON.stringify(collections), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching collections:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch collections',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Create collection
  if (
    normalizedPath === '/api/admin/collections' &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      const body = (await request.json()) as any
      const { name, status } = body

      if (!name) {
        return new Response(
          JSON.stringify({ error: 'Collection name is required.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const newCollection = await dbService.createCollection({
        name,
        status: status || 'ACTIVE',
      })

      return new Response(JSON.stringify(newCollection), {
        headers: { 'Content-Type': 'application/json' },
        status: 201,
      })
    } catch (error: any) {
      console.error('Error creating collection:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to create collection',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get collection by ID
  if (
    normalizedPath.match(/^\/api\/admin\/collections\/\d+$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const collectionId = Number.parseInt(
        normalizedPath.split('/').pop() || '0',
      )
      if (Number.isNaN(collectionId)) {
        return new Response(
          JSON.stringify({ error: 'Invalid collection ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const collection = await dbService.getCollectionById(collectionId)
      if (collection) {
        return new Response(JSON.stringify(collection), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      return new Response(JSON.stringify({ error: 'Collection not found.' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 404,
      })
    } catch (error: any) {
      console.error('Error fetching collection:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch collection',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update collection
  if (
    normalizedPath.match(/^\/api\/admin\/collections\/\d+$/) &&
    request.method === 'PUT'
  ) {
    try {
      // TODO: Add proper authentication check
      const collectionId = Number.parseInt(
        normalizedPath.split('/').pop() || '0',
      )
      if (Number.isNaN(collectionId)) {
        return new Response(
          JSON.stringify({ error: 'Invalid collection ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const body = (await request.json()) as any
      const { name, status } = body

      if (!name && !status) {
        return new Response(
          JSON.stringify({
            error:
              'No update data provided for collection (name or status required).',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const updatedCollection = await dbService.updateCollection(collectionId, {
        name,
        status,
      })

      if (updatedCollection) {
        return new Response(JSON.stringify(updatedCollection), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      return new Response(
        JSON.stringify({ error: 'Collection not found or update failed.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error('Error updating collection:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to update collection',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete collection
  if (
    normalizedPath.match(/^\/api\/admin\/collections\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check
      const collectionId = Number.parseInt(
        normalizedPath.split('/').pop() || '0',
      )
      if (Number.isNaN(collectionId)) {
        return new Response(
          JSON.stringify({ error: 'Invalid collection ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const deletedCollection = await dbService.deleteCollection(collectionId)
      if (deletedCollection) {
        return new Response(
          JSON.stringify({ message: 'Collection deleted successfully.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }

      return new Response(
        JSON.stringify({ error: 'Collection not found or delete failed.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error('Error deleting collection:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to delete collection',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // S3 Configuration Routes

  // Get S3 presets
  if (
    normalizedPath === '/api/admin/s3-configurations/presets' &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const s3Presets = {
        aws_s3: {
          serviceName: 'AWS S3',
          endpointUrl: null,
        },
        cloudflare_r2: {
          serviceName: 'Cloudflare R2',
          region: 'auto',
        },
        digitalocean_spaces: {
          serviceName: 'DigitalOcean Spaces',
        },
        other: {
          serviceName: 'Other S3 Compatible Storage',
        },
      }

      return new Response(JSON.stringify(s3Presets), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching S3 presets:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch S3 presets',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get all S3 Configurations
  if (
    normalizedPath === '/api/admin/s3-configurations' &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const configs = await dbService.getAllS3Configurations()
      // RBAC: Filter fields based on role (assuming admin role for now)
      // This logic needs to be adapted from the Express.js route if different roles are handled here
      return new Response(JSON.stringify(configs), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching S3 configurations:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch S3 configurations',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Create S3 Configuration
  if (
    normalizedPath === '/api/admin/s3-configurations' &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      const body = (await request.json()) as any
      const { serviceName, accessKeyId, secretAccessKey, bucketName } = body

      if (!serviceName || !accessKeyId || !secretAccessKey || !bucketName) {
        return new Response(
          JSON.stringify({
            error: 'Missing required S3 configuration fields.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const newConfig = await dbService.createS3Configuration(body)
      if (!newConfig) {
        return new Response(
          JSON.stringify({
            error:
              'Failed to create S3 configuration. Database service returned null.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      return new Response(JSON.stringify(newConfig), {
        headers: { 'Content-Type': 'application/json' },
        status: 201,
      })
    } catch (error: any) {
      console.error('Error creating S3 configuration:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to create S3 configuration',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get S3 Configuration by ID
  if (
    normalizedPath.match(/^\/api\/admin\/s3-configurations\/\d+$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const id = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(id)) {
        return new Response(
          JSON.stringify({ error: 'Invalid S3 configuration ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const config = await dbService.getS3ConfigurationById(id)
      if (config) {
        // RBAC: Filter fields based on role (assuming admin role for now)
        // This logic needs to be adapted from the Express.js route if different roles are handled here
        return new Response(JSON.stringify(config), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      return new Response(
        JSON.stringify({ error: 'S3 configuration not found.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error(
        `Error fetching S3 configuration ${normalizedPath.split('/').pop()}:`,
        error,
      )
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch S3 configuration',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update S3 Configuration
  if (
    normalizedPath.match(/^\/api\/admin\/s3-configurations\/\d+$/) &&
    request.method === 'PUT'
  ) {
    try {
      // TODO: Add proper authentication check
      const id = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(id)) {
        return new Response(
          JSON.stringify({ error: 'Invalid S3 configuration ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const body = (await request.json()) as any
      if (Object.keys(body).length === 0) {
        return new Response(
          JSON.stringify({
            error: 'No update data provided for S3 configuration.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const updatedConfig = await dbService.updateS3Configuration(id, body)
      if (updatedConfig) {
        return new Response(JSON.stringify(updatedConfig), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      return new Response(
        JSON.stringify({
          error: 'S3 configuration not found or update failed.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error(
        `Error updating S3 configuration ${normalizedPath.split('/').pop()}:`,
        error,
      )
      return new Response(
        JSON.stringify({
          error: 'Failed to update S3 configuration',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete S3 Configuration
  if (
    normalizedPath.match(/^\/api\/admin\/s3-configurations\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check
      const id = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(id)) {
        return new Response(
          JSON.stringify({ error: 'Invalid S3 configuration ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const deletedConfig = await dbService.deleteS3Configuration(id)
      if (deletedConfig) {
        return new Response(
          JSON.stringify({ message: 'S3 configuration deleted successfully.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({
          error: 'S3 configuration not found or delete failed.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error(
        `Error deleting S3 configuration ${normalizedPath.split('/').pop()}:`,
        error,
      )
      // Check for PostgreSQL foreign key constraint error
      if (
        error.code === '23503' ||
        error.message?.includes('foreign key constraint')
      ) {
        return new Response(
          JSON.stringify({
            error:
              'Cannot delete S3 configuration because it is currently in use by one or more documents.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 409,
          },
        )
      }
      return new Response(
        JSON.stringify({
          error: 'Failed to delete S3 configuration',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get Documents by Collection ID (paginated)
  if (
    normalizedPath.match(/^\/api\/admin\/collections\/\d+\/documents$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const pathParts = normalizedPath.split('/')
      const collectionId = Number.parseInt(pathParts[4]) // /api/admin/collections/{id}/documents
      const urlParams = new URL(request.url)
      const page = Number.parseInt(urlParams.searchParams.get('page') || '1')
      const limit = Number.parseInt(urlParams.searchParams.get('limit') || '10')

      if (Number.isNaN(collectionId)) {
        return new Response(
          JSON.stringify({ error: 'Invalid Collection ID.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Check if collection exists
      const collection = await dbService.getCollectionById(collectionId)
      if (!collection) {
        return new Response(
          JSON.stringify({ error: 'Collection not found.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 404,
          },
        )
      }

      const documents = await dbService.getDocumentsByCollectionId(
        collectionId,
        page,
        limit,
      )
      const total = await dbService.countDocumentsByCollectionId(collectionId)

      const response = {
        items: documents, // Changed from 'documents' to 'items' to match frontend expectations
        total,
        page,
        limit,
      }

      return new Response(JSON.stringify(response), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error(
        `Error fetching documents for collection ${normalizedPath.split('/')[4]}:`,
        error,
      )
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch documents',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Document Routes

  // Upload Document
  if (
    normalizedPath === '/api/admin/documents/upload' &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      // Note: This is a simplified version. The full implementation would need proper file upload handling
      // For now, we'll return a placeholder response
      return new Response(
        JSON.stringify({
          error: 'Document upload not yet implemented in Bun server',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 501,
        },
      )
    } catch (error: any) {
      console.error('Error uploading document:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to upload document',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete Document
  if (
    normalizedPath.match(/^\/api\/admin\/documents\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check
      const id = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (Number.isNaN(id)) {
        return new Response(JSON.stringify({ error: 'Invalid document ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const document = await dbService.getDocumentById(id)
      if (!document) {
        return new Response(JSON.stringify({ error: 'Document not found.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      if (!document.s3ConfigurationId) {
        return new Response(
          JSON.stringify({
            error:
              'Document is missing S3 configuration reference, cannot delete from S3.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      // Fetch S3 configuration to initialize S3Service
      const s3Config = await dbService.getS3ConfigurationById(
        document.s3ConfigurationId,
      )
      if (!s3Config) {
        return new Response(
          JSON.stringify({
            error: 'S3 configuration not found for this document.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 500,
          },
        )
      }

      // Initialize S3Service and delete from S3
      const s3Service = new (await import('./services/s3Service')).default(
        s3Config,
      )
      try {
        await s3Service.deleteFile(document.s3Key)
      } catch (error) {
        console.warn(`Failed to delete file from S3: ${error}`)
        // Continue with database deletion even if S3 deletion fails
      }

      // Delete from database
      const deletedDocument = await dbService.deleteDocument(id)
      if (deletedDocument) {
        return new Response(
          JSON.stringify({ message: 'Document deleted successfully.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }
      return new Response(
        JSON.stringify({ error: 'Document not found or delete failed.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        },
      )
    } catch (error: any) {
      console.error(
        `Error deleting document ${normalizedPath.split('/').pop()}:`,
        error,
      )
      return new Response(
        JSON.stringify({
          error: 'Failed to delete document',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Service Management Routes

  // Get all services
  if (normalizedPath === '/api/admin/services' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check
      const services = await dbService.getAllServices()
      return new Response(JSON.stringify(services), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching services:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch services',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Create service
  if (normalizedPath === '/api/admin/services' && request.method === 'POST') {
    try {
      // TODO: Add proper authentication check
      const body = (await request.json()) as any
      const { name, type, description, isActive, configuration } = body

      if (!name || !type || !configuration) {
        return new Response(
          JSON.stringify({
            error: 'Name, type, and configuration are required.',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Validate service type
      const validTypes = ['R2R_RAG', 'SMTP_PROVIDER', 'EXTERNAL_API']
      if (!validTypes.includes(type)) {
        return new Response(
          JSON.stringify({ error: 'Invalid service type.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const newService = await dbService.createService({
        name,
        type,
        description,
        isActive,
        configuration,
      })

      return new Response(JSON.stringify(newService), {
        headers: { 'Content-Type': 'application/json' },
        status: 201,
      })
    } catch (error: any) {
      console.error('Error creating service:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to create service',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get service by ID
  if (
    normalizedPath.match(/^\/api\/admin\/services\/\d+$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const serviceId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (serviceId === 0) {
        return new Response(JSON.stringify({ error: 'Invalid service ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const service = await dbService.getServiceById(serviceId)
      if (!service) {
        return new Response(JSON.stringify({ error: 'Service not found.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(service), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching service:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch service',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update service
  if (
    normalizedPath.match(/^\/api\/admin\/services\/\d+$/) &&
    request.method === 'PUT'
  ) {
    try {
      // TODO: Add proper authentication check
      const serviceId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (serviceId === 0) {
        return new Response(JSON.stringify({ error: 'Invalid service ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const body = (await request.json()) as any
      const { name, description, isActive, configuration } = body

      const updatedService = await dbService.updateService(serviceId, {
        name,
        description,
        isActive,
        configuration,
      })

      if (!updatedService) {
        return new Response(JSON.stringify({ error: 'Service not found.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(updatedService), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error updating service:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to update service',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete service
  if (
    normalizedPath.match(/^\/api\/admin\/services\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check
      const serviceId = Number.parseInt(normalizedPath.split('/').pop() || '0')
      if (serviceId === 0) {
        return new Response(JSON.stringify({ error: 'Invalid service ID.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const success = await dbService.deleteService(serviceId)
      if (!success) {
        return new Response(JSON.stringify({ error: 'Service not found.' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(
        JSON.stringify({ message: 'Service deleted successfully.' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error deleting service:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to delete service',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Session Management Routes

  // Get agent dashboard statistics
  if (
    normalizedPath === '/api/admin/sessions/stats' &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      // Get real stats from database
      const stats = await dbService.getSessionStats()

      return new Response(JSON.stringify(stats), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching dashboard stats:', error)
      // Return mock stats as fallback
      const fallbackStats = {
        totalSessions: 0,
        activeSessions: 0,
        pendingHandovers: 0,
        completedToday: 0,
      }
      return new Response(JSON.stringify(fallbackStats), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }
  }

  // Get all sessions (admin endpoint)
  if (normalizedPath === '/api/admin/sessions' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check
      const sessions = await dbService.getAllChatSessions()
      return new Response(JSON.stringify(sessions), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching sessions:', error)
      // Return mock data for testing when database is not available
      const mockSessions = [
        {
          id: 'session-1',
          userId: 'user-123',
          platform: 'web',
          platformId: null,
          status: 'active',
          isHandedOver: false,
          createdAt: new Date().toISOString(),
          lastMessageAt: new Date().toISOString(),
          messageCount: 5,
          agentId: null,
          agentName: null,
        },
        {
          id: 'session-2',
          userId: 'user-456',
          platform: 'facebook',
          platformId: 'fb-user-456',
          status: 'completed',
          isHandedOver: true,
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          lastMessageAt: new Date(Date.now() - 1800000).toISOString(),
          messageCount: 12,
          agentId: 1,
          agentName: 'Agent Smith',
        },
        {
          id: 'session-3',
          userId: null,
          platform: 'whatsapp',
          platformId: '+60123456789',
          status: 'pending',
          isHandedOver: false,
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          lastMessageAt: new Date(Date.now() - 3600000).toISOString(),
          messageCount: 3,
          agentId: null,
          agentName: null,
        },
      ]

      return new Response(JSON.stringify(mockSessions), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }
  }

  // Get pending handover requests
  if (
    normalizedPath === '/api/admin/sessions/handovers/pending' &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const requests = await dbService.getPendingHandoverRequests()
      return new Response(JSON.stringify(requests), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching pending handovers:', error)
      // Return mock handover requests for testing
      const mockRequests = [
        {
          id: 1,
          sessionId: 'session-1',
          requestedBy: 'user-123',
          reason: 'Need technical support',
          priority: 'high',
          status: 'pending',
          createdAt: new Date(Date.now() - 1800000).toISOString(),
        },
        {
          id: 2,
          sessionId: 'session-3',
          requestedBy: 'user-789',
          reason: 'Complex certification inquiry',
          priority: 'normal',
          status: 'pending',
          createdAt: new Date(Date.now() - 3600000).toISOString(),
        },
      ]

      return new Response(JSON.stringify(mockRequests), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }
  }

  // Create handover request
  if (
    normalizedPath === '/api/admin/sessions/handover' &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      const body = (await request.json()) as any
      const { sessionId, reason, priority } = body

      if (!sessionId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Session ID is required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const handoverRequestId = await dbService.createHandoverRequest(
        sessionId,
        'user',
        reason,
        priority || 'normal',
      )

      return new Response(
        JSON.stringify({
          success: true,
          handoverRequestId,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error creating handover request:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to create handover request',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get specific session details
  if (
    normalizedPath.match(/^\/api\/admin\/sessions\/[^/]+$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check
      const sessionId = normalizedPath.split('/').pop()
      if (!sessionId) {
        return new Response(
          JSON.stringify({ error: 'Session ID is required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const session = await dbService.getChatSession(sessionId)
      if (!session) {
        // Return mock session data for testing when database is not available
        const mockSession = {
          id: sessionId,
          userId: 'user-123',
          platform: 'web',
          platformId: null,
          status: 'active',
          isHandedOver: false,
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          lastMessageAt: new Date().toISOString(),
          agentId: null,
          agentName: null,
          messages: [
            {
              id: 'msg-1',
              role: 'user',
              content: 'Hello, I need help with halal certification.',
              timestamp: new Date(Date.now() - 3600000).toISOString(),
              agentId: null,
              agentName: null,
            },
            {
              id: 'msg-2',
              role: 'assistant',
              content:
                "Hello! I'd be happy to help you with halal certification. What specific information do you need?",
              timestamp: new Date(Date.now() - 3500000).toISOString(),
              agentId: null,
              agentName: null,
            },
            {
              id: 'msg-3',
              role: 'user',
              content:
                'I want to know about the requirements for food manufacturing.',
              timestamp: new Date(Date.now() - 3400000).toISOString(),
              agentId: null,
              agentName: null,
            },
            {
              id: 'msg-4',
              role: 'assistant',
              content:
                'For halal food manufacturing certification, you need to meet several requirements including proper sourcing of ingredients, halal-compliant production processes, and regular audits. Would you like me to provide more detailed information about any specific aspect?',
              timestamp: new Date(Date.now() - 3300000).toISOString(),
              agentId: null,
              agentName: null,
            },
            {
              id: 'msg-5',
              role: 'user',
              content:
                'Yes, please tell me about the ingredient sourcing requirements.',
              timestamp: new Date(Date.now() - 1800000).toISOString(),
              agentId: null,
              agentName: null,
            },
            {
              id: 'msg-6',
              role: 'agent',
              content:
                'I can help you with detailed ingredient sourcing requirements. All ingredients must be certified halal, and you need to maintain proper documentation for traceability.',
              timestamp: new Date().toISOString(),
              agentId: 1,
              agentName: 'Agent Smith',
            },
          ],
        }

        return new Response(JSON.stringify(mockSession), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      return new Response(JSON.stringify(session), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error(
        `Error fetching session ${normalizedPath.split('/').pop()}:`,
        error,
      )
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch session',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Send message as agent
  if (
    normalizedPath.match(/^\/api\/admin\/sessions\/[^/]+\/message$/) &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      const pathParts = normalizedPath.split('/')
      const sessionId = pathParts[pathParts.length - 2] // Get sessionId from path
      const body = (await request.json()) as any
      const { content, type, fileUrl, fileName, audioUrl } = body

      if (!content) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Message content is required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // For now, use a placeholder agent ID - this should come from authentication
      const agentId = 1 // TODO: Get from authenticated user

      // Get session details to determine platform
      const session = await dbService.getChatSession(sessionId)
      if (!session) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Session not found',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 404,
          },
        )
      }

      // Add message to database
      const messageId = await dbService.addChatMessage(
        sessionId,
        'agent',
        content,
        agentId,
        undefined, // imageUrl
        audioUrl,
        fileUrl,
        fileName,
      )

      // If this is a Facebook session, send the message via Facebook API
      if (session.platform === 'facebook' && session.platformId) {
        try {
          const facebookService = (await import('./services/facebook')).default
          const sendResult = await facebookService.sendTextMessage(
            session.platformId,
            content
          )

          if (!sendResult.success) {
            console.error('Failed to send Facebook message:', sendResult.error)
            // Don't fail the request, message is still saved in database
          } else {
            console.log(`Sent agent message to Facebook user ${session.platformId}`)
          }
        } catch (error) {
          console.error('Error sending Facebook message:', error)
          // Don't fail the request, message is still saved in database
        }
      }

      return new Response(
        JSON.stringify({
          success: true,
          messageId,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error sending agent message:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to send message',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Complete session (return to bot)
  if (
    normalizedPath.match(/^\/api\/admin\/sessions\/[^/]+\/complete$/) &&
    request.method === 'POST'
  ) {
    try {
      // TODO: Add proper authentication check
      const pathParts = normalizedPath.split('/')
      const sessionId = pathParts[pathParts.length - 2] // Get sessionId from path

      await dbService.completeSessionAssignment(sessionId)

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error completing session:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to complete session',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Additional Admin Routes

  // Get admin dashboard overview
  if (normalizedPath === '/api/admin/dashboard' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check
      // Return basic dashboard data
      const dashboard = {
        totalUsers: 0,
        totalSessions: 0,
        totalCollections: 0,
        totalDocuments: 0,
        recentActivity: [],
      }

      return new Response(JSON.stringify(dashboard), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching dashboard:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch dashboard',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get system settings
  if (normalizedPath === '/api/admin/settings' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check
      const settings = {
        siteName: 'Halal Malaysia Portal',
        version: '1.0.0',
        features: {
          whatsapp: true,
          facebook: true,
          chat: true,
          documents: true,
        },
      }

      return new Response(JSON.stringify(settings), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching settings:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch settings',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update system settings
  if (normalizedPath === '/api/admin/settings' && request.method === 'PUT') {
    try {
      // TODO: Add proper authentication check
      const body = (await request.json()) as any

      // For now, just return success - implement actual settings update later
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Settings updated successfully',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error updating settings:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to update settings',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Site Management Routes

  // Get all sites
  if (normalizedPath === '/api/admin/sites' && request.method === 'GET') {
    try {
      // TODO: Add proper authentication check for superadmin role
      const sites = await dbService.getAllSites()
      return new Response(
        JSON.stringify({
          data: sites,
          total: sites.length,
          page: 1,
          limit: sites.length,
          totalPages: 1,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error fetching sites:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch sites',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Get site by ID
  if (
    normalizedPath.match(/^\/api\/admin\/sites\/\d+$/) &&
    request.method === 'GET'
  ) {
    try {
      // TODO: Add proper authentication check for superadmin role
      const siteIdFromPath = normalizedPath.split('/').pop()
      if (!siteIdFromPath) {
        return new Response(JSON.stringify({ error: 'Invalid site ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const site = await dbService.getSiteById(
        Number.parseInt(siteIdFromPath, 10),
      )
      if (!site) {
        return new Response(JSON.stringify({ error: 'Site not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(site), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error fetching site:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch site',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Create new site
  if (normalizedPath === '/api/admin/sites' && request.method === 'POST') {
    try {
      // TODO: Add proper authentication check for superadmin role
      const body = (await request.json()) as any
      const { name, code, domains, status } = body

      if (!name || !code || !domains) {
        return new Response(
          JSON.stringify({ error: 'Name, code, and domains are required.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      if (!Array.isArray(domains)) {
        return new Response(
          JSON.stringify({ error: 'Domains must be an array.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const newSite = await dbService.createSite({
        name,
        code,
        domains,
        status,
      })
      return new Response(JSON.stringify(newSite), {
        headers: { 'Content-Type': 'application/json' },
        status: 201,
      })
    } catch (error: any) {
      console.error('Error creating site:', error)
      if (error.message?.includes('already exists')) {
        return new Response(JSON.stringify({ error: error.message }), {
          headers: { 'Content-Type': 'application/json' },
          status: 409,
        })
      }
      return new Response(
        JSON.stringify({
          error: 'Failed to create site',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Update site
  if (
    normalizedPath.match(/^\/api\/admin\/sites\/\d+$/) &&
    request.method === 'PUT'
  ) {
    try {
      // TODO: Add proper authentication check for superadmin role
      const siteIdFromPath = normalizedPath.split('/').pop()
      if (!siteIdFromPath) {
        return new Response(JSON.stringify({ error: 'Invalid site ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const body = (await request.json()) as any
      const { name, code, domains, status } = body

      if (!name || !code || !domains) {
        return new Response(
          JSON.stringify({ error: 'Name, code, and domains are required.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      if (!Array.isArray(domains)) {
        return new Response(
          JSON.stringify({ error: 'Domains must be an array.' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const updatedSite = await dbService.updateSite(
        Number.parseInt(siteIdFromPath, 10),
        {
          name,
          code,
          domains,
          status,
        },
      )

      if (!updatedSite) {
        return new Response(JSON.stringify({ error: 'Site not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(updatedSite), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error: any) {
      console.error('Error updating site:', error)
      if (error.message?.includes('already exists')) {
        return new Response(JSON.stringify({ error: error.message }), {
          headers: { 'Content-Type': 'application/json' },
          status: 409,
        })
      }
      return new Response(
        JSON.stringify({
          error: 'Failed to update site',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Delete site
  if (
    normalizedPath.match(/^\/api\/admin\/sites\/\d+$/) &&
    request.method === 'DELETE'
  ) {
    try {
      // TODO: Add proper authentication check for superadmin role
      const siteIdFromPath = normalizedPath.split('/').pop()
      if (!siteIdFromPath) {
        return new Response(JSON.stringify({ error: 'Invalid site ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      const deleted = await dbService.deleteSite(
        Number.parseInt(siteIdFromPath, 10),
      )
      if (!deleted) {
        return new Response(JSON.stringify({ error: 'Site not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(
        JSON.stringify({ message: 'Site deleted successfully' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error: any) {
      console.error('Error deleting site:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to delete site',
          message: error.message,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin agents routes
  if (normalizedPath.startsWith('/api/admin/agents')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        c.set('env', env)
        await next()
      })

      // Mount the admin agents router
      router.route('/', adminAgentsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/agents', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest)
    } catch (error) {
      console.error('Error handling admin agents route:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to handle agents route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin bots routes
  if (normalizedPath.startsWith('/api/admin/bots')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        await next()
      })

      // Mount the admin bots router
      router.route('/', adminBotsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/bots', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest)
    } catch (error) {
      console.error('Error handling admin bots route:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to handle bots route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin contacts routes
  if (normalizedPath.startsWith('/api/admin/contacts')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        c.set('env', env)
        await next()
      })

      // Mount the admin contacts router
      router.route('/', adminContactsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/contacts', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest)
    } catch (error) {
      console.error('Error handling admin contacts route:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to handle contacts route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin teams routes
  if (normalizedPath.startsWith('/api/admin/teams')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        c.set('env', env)
        await next()
      })

      // Mount the admin teams router
      router.route('/', adminTeamsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/teams', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest, env)
    } catch (error: any) {
      console.error('Error handling teams route:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Internal server error',
          message: 'Failed to handle teams route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin collections routes
  if (normalizedPath.startsWith('/api/admin/collections')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        c.set('env', env)
        await next()
      })

      // Mount the admin collections router
      router.route('/', adminCollectionsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/collections', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest)
    } catch (error) {
      console.error('Error handling admin collections route:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to handle collections route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle admin documents routes
  if (normalizedPath.startsWith('/api/admin/documents')) {
    try {
      const router = new Hono()

      // Set up middleware
      router.use('*', async (c, next) => {
        c.set('db', dbService.db)
        c.set('siteId', siteId)
        c.set('env', env)
        await next()
      })

      // Mount the admin documents router
      router.route('/', adminDocumentsRouter)

      // Create a new request with the path adjusted for the router
      const adjustedPath = normalizedPath.replace('/api/admin/documents', '')
      const adjustedUrl = new URL(request.url)
      adjustedUrl.pathname = adjustedPath || '/'

      const adjustedRequest = new Request(adjustedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body,
      })

      return await router.fetch(adjustedRequest)
    } catch (error) {
      console.error('Error handling admin documents route:', error)
      return new Response(
        JSON.stringify({
          error: 'Internal server error',
          message: 'Failed to handle documents route',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Default response for other admin routes
  return new Response(JSON.stringify({ error: 'Admin route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

async function handleAgentRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname
  const method = request.method

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  try {
    // Import agent auth service
    const agentAuthService = (await import('./services/agentAuth')).default

    // Agent login
    if (normalizedPath === '/api/agent/login' && method === 'POST') {
      const body = (await request.json()) as any
      const credentials = body as AgentLoginRequest
      const result = await agentAuthService.login(
        credentials,
        dbService,
        env,
        siteId,
      )

      const status = result.success ? 200 : 401
      return new Response(JSON.stringify(result), {
        headers: { 'Content-Type': 'application/json' },
        status,
      })
    }

    // Agent logout
    if (path === '/api/agent/logout' && method === 'POST') {
      const authHeader = request.headers.get('authorization')
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7)
        const decoded = agentAuthService.verifyToken(token, env)
        if (decoded) {
          await agentAuthService.logout(decoded.agentId, dbService)
        }
      }

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Agent profile
    if (path === '/api/agent/profile' && method === 'GET') {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const token = authHeader.substring(7)
      const decoded = agentAuthService.verifyToken(token, env)
      if (!decoded) {
        return new Response(JSON.stringify({ error: 'Invalid token' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 401,
        })
      }

      const agent = await agentAuthService.getAgentProfile(
        decoded.agentId,
        dbService,
      )
      if (!agent) {
        return new Response(JSON.stringify({ error: 'Agent not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(agent), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Update agent status
    if (path === '/api/agent/status' && method === 'POST') {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const token = authHeader.substring(7)
      const decoded = agentAuthService.verifyToken(token, env)
      if (!decoded) {
        return new Response(JSON.stringify({ error: 'Invalid token' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 401,
        })
      }

      const body = (await request.json()) as any
      const { isOnline } = body

      if (typeof isOnline !== 'boolean') {
        return new Response(
          JSON.stringify({ error: 'isOnline must be a boolean' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      await agentAuthService.updateOnlineStatus(
        decoded.agentId,
        isOnline,
        dbService,
      )

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Verify token
    if (path === '/api/agent/verify' && method === 'GET') {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const token = authHeader.substring(7)
      const decoded = agentAuthService.verifyToken(token, env)
      if (!decoded) {
        return new Response(JSON.stringify({ error: 'Invalid token' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 401,
        })
      }

      return new Response(JSON.stringify({ success: true, agent: decoded }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Default response for unknown agent routes
    return new Response(JSON.stringify({ error: 'Agent route not found' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 404,
    })
  } catch (error) {
    console.error('Agent request error:', error)
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    })
  }
}

async function handleSessionRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path
  const method = request.method

  try {
    // Import agent auth service
    const agentAuthService = (await import('./services/agentAuth')).default

    // Helper function to authenticate agent
    const authenticateAgent = (request: Request) => {
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null
      }
      const token = authHeader.substring(7)
      return agentAuthService.verifyToken(token, env)
    }

    // Get dashboard stats
    if (normalizedPath === '/api/sessions/stats' && method === 'GET') {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      // Get actual stats from database
      const stats = await dbService.getSessionStats()

      return new Response(JSON.stringify(stats), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Get pending handover requests
    if (
      normalizedPath === '/api/sessions/handovers/pending' &&
      method === 'GET'
    ) {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const requests = await dbService.getPendingHandoverRequests()
      return new Response(JSON.stringify(requests), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Create handover request
    if (normalizedPath === '/api/sessions/handover' && method === 'POST') {
      const body = (await request.json()) as any
      const { sessionId, reason, priority } = body

      if (!sessionId) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Session ID is required',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      const handoverRequestId = await dbService.createHandoverRequest(
        sessionId,
        'user',
        reason,
        priority || 'normal',
      )

      return new Response(
        JSON.stringify({
          success: true,
          handoverRequestId,
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // Assign handover request to agent
    if (
      path.match(/^\/api\/sessions\/handovers\/\d+\/assign$/) &&
      method === 'POST'
    ) {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const requestId = Number.parseInt(path.split('/')[4])
      if (Number.isNaN(requestId)) {
        return new Response(JSON.stringify({ error: 'Invalid request ID' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        })
      }

      // Use team-aware assignment with validation
      const enforceTeamConstraints = env.ENFORCE_TEAM_CONSTRAINTS !== 'false' // Default to true
      const assignmentResult = await dbService.assignHandoverRequestWithTeamValidation(
        requestId,
        agent.agentId,
        enforceTeamConstraints
      )

      if (!assignmentResult.success) {
        return new Response(
          JSON.stringify({
            success: false,
            error: assignmentResult.error || 'Failed to assign handover request',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Get agent's active sessions
    if (normalizedPath === '/api/sessions/active' && method === 'GET') {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const sessions = await dbService.getAgentActiveSessions(agent.agentId)
      return new Response(JSON.stringify(sessions), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Get all active sessions (for agent takeover)
    if (normalizedPath === '/api/sessions/all-active' && method === 'GET') {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const sessions = await dbService.getAllActiveSessions()
      return new Response(JSON.stringify(sessions), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Take over a session (assign to agent)
    if (
      normalizedPath.match(/^\/api\/sessions\/[^/]+\/takeover$/) &&
      method === 'POST'
    ) {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const sessionId = path.split('/')[3]

      // Check if session exists and is active
      const session = await dbService.getChatSession(sessionId)
      if (!session) {
        return new Response(JSON.stringify({ error: 'Session not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      if (session.status !== 'active') {
        return new Response(
          JSON.stringify({ error: 'Session is not active' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Check if session is already assigned to an agent
      const existingAssignment = await dbService.getAgentActiveSessions(
        agent.agentId,
      )
      const isAlreadyAssigned = existingAssignment.some(
        (s) => s.id === sessionId,
      )

      if (isAlreadyAssigned) {
        return new Response(
          JSON.stringify({ error: 'Session is already assigned to you' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Use team-aware session assignment with validation
      const enforceTeamConstraints = env.ENFORCE_TEAM_CONSTRAINTS !== 'false' // Default to true
      const assignmentResult = await dbService.assignSessionToAgentWithTeamValidation(
        sessionId,
        agent.agentId,
        enforceTeamConstraints
      )

      if (!assignmentResult.success) {
        return new Response(
          JSON.stringify({
            success: false,
            error: assignmentResult.error || 'Failed to assign session',
          }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Session taken over successfully',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    }

    // Get specific session details
    if (normalizedPath.match(/^\/api\/sessions\/[^/]+$/) && method === 'GET') {
      const agent = authenticateAgent(request)
      if (!agent) {
        return new Response(
          JSON.stringify({ error: 'Authentication required' }),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 401,
          },
        )
      }

      const sessionId = path.split('/')[3]
      const session = await dbService.getChatSession(sessionId)

      if (!session) {
        return new Response(JSON.stringify({ error: 'Session not found' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 404,
        })
      }

      return new Response(JSON.stringify(session), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    }

    // Default response for unknown session routes
    return new Response(JSON.stringify({ error: 'Session route not found' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 404,
    })
  } catch (error) {
    console.error('Session request error:', error)
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    })
  }
}

async function handleSearchRequest(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Handle search endpoint
  if (path === '/api/search' && request.method === 'GET') {
    const {
      query,
      retrieveDocument = 'true',
      maxWordCount = '3000',
      includeGraph = 'true',
    } = Object.fromEntries(url.searchParams)
    console.log('Received search query:', query)

    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Query parameter is missing' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 400,
        },
      )
    }

    const { R2R_USERNAME, R2R_PASSWORD, R2R_URL, R2R_COLLECTION_ID } = env

    if (!R2R_USERNAME || !R2R_PASSWORD || !R2R_URL) {
      console.error('R2R credentials not configured')
      return new Response(
        JSON.stringify({ error: 'R2R credentials not configured' }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }

    try {
      const r2rService = new R2RService()

      // Perform search
      const { chunks, graph } = await r2rService.search(
        query as string,
        {
          retrieveDocument: retrieveDocument === 'true',
          maxWordCount: Number.parseInt(maxWordCount as string, 10),
          includeGraph: includeGraph === 'true',
          collectionId: R2R_COLLECTION_ID,
        },
        env,
      )

      console.log('Extracted chunks:', chunks.length)
      console.log('Extracted graph results:', graph.length)

      // Parse options
      const options: ParseR2rOptions = {
        retrieveDocument: retrieveDocument === 'true',
        maxWordCount: Number.parseInt(maxWordCount as string, 10),
        includeGraph: includeGraph === 'true',
        minScore: 0.3, // Set a default minimum score
        limit: 20,
      }

      // Get the R2R client for parsing
      const client = r2rService.getClient(env)
      const r2rResult = await r2rService.parseR2rResult(
        client,
        chunks,
        options.includeGraph ? graph : [],
        options,
      )

      const searchResponse: SearchResponse = {
        query: query as string,
        results: r2rResult.texts,
        totalChunks: chunks.length,
        totalGraphResults: graph.length,
        options,
      }

      console.log('Final search response:', {
        query,
        resultsCount: r2rResult.texts.length,
        totalChunks: chunks.length,
        totalGraphResults: graph.length,
        results: r2rResult.texts,
      })

      return new Response(JSON.stringify(searchResponse), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error) {
      console.error('R2R search error:', error)
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch search results',
          details: error instanceof Error ? error.message : 'Unknown error',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Default response for unknown search routes
  return new Response(JSON.stringify({ error: 'Search route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

async function handleHalalKnowledgeRequest(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Handle halal knowledge ask endpoint
  if (path === '/api/halal-knowledge/ask' && request.method === 'POST') {
    try {
      const body = (await request.json()) as any
      const {
        query,
        sessionId,
        maxResults = 5,
        minScore = 0.3,
        includeContext = true,
      } = body

      // Validate required fields
      if (!query || typeof query !== 'string' || query.trim().length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Query is required and must be a non-empty string',
            query: query || '',
            sessionId,
          } as HalalKnowledgeResponse),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 400,
          },
        )
      }

      // Check if the query is related to halal topics
      const halalKnowledgeService = new HalalKnowledgeService()
      if (!halalKnowledgeService.isHalalRelatedQuery(query)) {
        return new Response(
          JSON.stringify({
            success: true,
            answer:
              "I specialize in answering questions about halal and Islamic matters. Your question doesn't seem to be related to halal topics. Please ask me about Islamic jurisprudence, halal food, Islamic practices, or other religious matters.",
            sources: [],
            query,
            sessionId,
          } as HalalKnowledgeResponse),
          {
            headers: { 'Content-Type': 'application/json' },
            status: 200,
          },
        )
      }

      console.log('Processing halal knowledge request:', {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        sessionId,
        maxResults,
        minScore,
        includeContext,
      })

      // Process the request
      const response = await halalKnowledgeService.searchAndAnswer(
        {
          query,
          sessionId,
          maxResults,
          minScore,
          includeContext,
        },
        env,
      )

      console.log('Halal knowledge response:', {
        success: response.success,
        hasAnswer: !!response.answer,
        sourcesCount: response.sources?.length || 0,
        error: response.error,
      })

      return new Response(JSON.stringify(response), {
        headers: { 'Content-Type': 'application/json' },
        status: response.success ? 200 : 500,
      })
    } catch (error) {
      console.error('Halal knowledge endpoint error:', error)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Internal server error',
          query: '',
          sessionId: undefined,
        } as HalalKnowledgeResponse),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 500,
        },
      )
    }
  }

  // Handle halal knowledge health check endpoint
  if (path === '/api/halal-knowledge/health' && request.method === 'GET') {
    try {
      // Test R2R connection
      const testQuery = 'halal'
      const halalKnowledgeService = new HalalKnowledgeService()
      const testResponse = await halalKnowledgeService.searchAndAnswer(
        {
          query: testQuery,
          maxResults: 1,
          minScore: 0.1,
          includeContext: false,
        },
        env,
      )

      return new Response(
        JSON.stringify({
          status: 'healthy',
          timestamp: new Date().toISOString(),
          services: {
            r2r: testResponse.success ? 'connected' : 'error',
            openai: 'available',
          },
          message: 'Halal knowledge service is operational',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
        },
      )
    } catch (error) {
      console.error('Halal knowledge health check error:', error)
      return new Response(
        JSON.stringify({
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error',
          message: 'Halal knowledge service is experiencing issues',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 503,
        },
      )
    }
  }

  // Handle halal knowledge info endpoint
  if (path === '/api/halal-knowledge/info' && request.method === 'GET') {
    return new Response(
      JSON.stringify({
        service: 'Halal Knowledge Search',
        description: 'AI-powered halal knowledge search using R2R and OpenAI',
        version: '1.0.0',
        capabilities: [
          'Search halal knowledge base',
          'Generate AI-powered answers',
          'Provide source citations',
          'Filter by relevance score',
          'Support for multiple Islamic topics',
        ],
        supportedTopics: [
          'Halal and Haram rulings',
          'Islamic jurisprudence (Fiqh)',
          'Food and dietary laws',
          'Business and finance',
          'Prayer and worship',
          'Marriage and family',
          'Clothing and modesty',
          'General Islamic guidance',
        ],
        endpoints: {
          ask: 'POST /api/halal-knowledge/ask - Ask a halal-related question',
          health: 'GET /api/halal-knowledge/health - Check service health',
          info: 'GET /api/halal-knowledge/info - Get service information',
        },
      }),
      {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  }

  // Default response for unknown halal knowledge routes
  return new Response(
    JSON.stringify({ error: 'Halal knowledge route not found' }),
    {
      headers: { 'Content-Type': 'application/json' },
      status: 404,
    },
  )
}

async function handleSecurityRoutes(
  request: Request,
  env: any,
): Promise<Response> {
  const url = new URL(request.url)
  const path = url.pathname

  // Security health check endpoint
  if (path === '/api/security/health' && request.method === 'GET') {
    try {
      // Get basic system health indicators
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        checks: {
          memory: {
            status: 'healthy',
            heapUsed: process.memoryUsage?.()?.heapUsed || 0,
            heapTotal: process.memoryUsage?.()?.heapTotal || 0,
          },
          uptime: {
            status: 'healthy',
            uptime: Date.now(), // Worker start time approximation
          },
          database: {
            status: 'healthy',
            connected: true,
          },
        },
      }

      return new Response(JSON.stringify(health), {
        headers: { 'Content-Type': 'application/json' },
        status: 200,
      })
    } catch (error) {
      console.error('Security health check error:', error)
      return new Response(
        JSON.stringify({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: 'Health check failed',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
          status: 503,
        },
      )
    }
  }

  // Default response for unknown security routes
  return new Response(JSON.stringify({ error: 'Security route not found' }), {
    headers: { 'Content-Type': 'application/json' },
    status: 404,
  })
}

async function handleBotsRequest(
  request: Request,
  env: any,
  dbService: any,
  siteId: string,
): Promise<Response> {
  const { and, eq } = await import('drizzle-orm')
  const { bots } = await import('./db/schema')

  const url = new URL(request.url)
  const path = url.pathname

  // Remove site prefix from path for route matching
  const normalizedPath = path.includes(`/api/sites/${siteId}`)
    ? path.replace(`/api/sites/${siteId}`, '/api')
    : path

  console.log('handleBotsRequest - Original path:', path)
  console.log('handleBotsRequest - Normalized path:', normalizedPath)
  console.log('handleBotsRequest - Method:', request.method)

  // A simple routing mechanism
  if (normalizedPath.startsWith('/api/bots')) {
    if (request.method === 'POST' && normalizedPath === '/api/bots') {
      const { name, slug, provider, model, temperature, isDefault } =
        await request.json()
      const [newBot] = await dbService.db
        .insert(bots)
        .values({
          siteId: Number.parseInt(siteId),
          name,
          slug,
          provider,
          model,
          temperature,
          isDefault,
        })
        .returning()
      return new Response(JSON.stringify(newBot), {
        headers: { 'Content-Type': 'application/json' },
      })
    }
    if (request.method === 'GET' && normalizedPath === '/api/bots') {
      const allBots = await dbService.db
        .select()
        .from(bots)
        .where(eq(bots.siteId, Number.parseInt(siteId)))
      return new Response(JSON.stringify(allBots), {
        headers: { 'Content-Type': 'application/json' },
      })
    }
    if (
      request.method === 'GET' &&
      normalizedPath.match(/^\/api\/bots\/slug\/[^/]+$/)
    ) {
      const slug = normalizedPath.split('/').pop() || ''
      const [bot] = await dbService.db
        .select()
        .from(bots)
        .where(
          and(eq(bots.slug, slug), eq(bots.siteId, Number.parseInt(siteId))),
        )
      return new Response(JSON.stringify(bot), {
        headers: { 'Content-Type': 'application/json' },
      })
    }
    if (request.method === 'GET' && normalizedPath === '/api/bots/default') {
      const [defaultBot] = await dbService.db
        .select()
        .from(bots)
        .where(
          and(
            eq(bots.isDefault, true),
            eq(bots.siteId, Number.parseInt(siteId)),
          ),
        )
        .limit(1)
      return new Response(JSON.stringify(defaultBot), {
        headers: { 'Content-Type': 'application/json' },
      })
    }
    if (
      request.method === 'POST' &&
      normalizedPath.match(/^\/api\/bots\/[^/]+\/chat\/message$/)
    ) {
      console.log('Bot chat message endpoint matched:', normalizedPath)
      // Extract botSlug from path: /api/bots/{botSlug}/chat/message
      const pathParts = normalizedPath.split('/')
      const botSlug = pathParts[3] // /api/bots/{botSlug}/chat/message
      console.log('Extracted botSlug:', botSlug)

      // Forward to the regular chat handler with botSlug in the body
      const body = await request.json()
      const modifiedBody = { ...body, botSlug }

      // Replace the original path with the target chat path
      const originalPath = `/api/sites/${siteId}/bots/${botSlug}/chat/message`
      const newUrl = request.url.replace(originalPath, '/api/chat/message')
      console.log('Forwarding from:', request.url, 'to:', newUrl)
      const modifiedRequest = new Request(newUrl, {
        method: 'POST',
        headers: request.headers,
        body: JSON.stringify(modifiedBody),
      })
      // console.log('Modified body:', modifiedBody);

      return await handleChatRequest(modifiedRequest, env, dbService, siteId)
    }
    if (
      request.method === 'POST' &&
      normalizedPath.match(/^\/api\/bots\/[^/]+\/chat\/image$/)
    ) {
      // Extract botSlug from path: /api/bots/{botSlug}/chat/image
      const pathParts = normalizedPath.split('/')
      const botSlug = pathParts[3] // /api/bots/{botSlug}/chat/image

      // Forward to the regular chat handler with botSlug in the body
      const body = await request.json()
      const modifiedBody = { ...body, botSlug }

      // Replace the original path with the target chat path
      const originalPath = `/api/sites/${siteId}/bots/${botSlug}/chat/image`
      const newUrl = request.url.replace(originalPath, '/api/chat/image')

      const modifiedRequest = new Request(newUrl, {
        method: 'POST',
        headers: request.headers,
        body: JSON.stringify(modifiedBody),
      })

      return await handleChatRequest(modifiedRequest, env, dbService, siteId)
    }
  }

  console.log(
    'Bot route not found - path:',
    normalizedPath,
    'method:',
    request.method,
  )
  return new Response(
    JSON.stringify({
      error: 'Bot route not found',
      path: normalizedPath,
      method: request.method,
    }),
    {
      headers: { 'Content-Type': 'application/json' },
      status: 404,
    },
  )
}

const port = Number(process.env.PORT) || 16001

console.log(`🚀 Server running on port ${port} with WebSocket support`)

// Export for Bun
export default {
  port,
  fetch: app.fetch,
  websocket,
}

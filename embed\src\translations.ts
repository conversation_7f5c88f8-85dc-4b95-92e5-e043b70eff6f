export const translations = {
  en: {
    welcome: "Hello! I'm your AI assistant for Halal inquiries. How can I help you today?\n\n💬 You can also reach me on WhatsApp for faster responses!",
    placeholder: "Type your message...",
    send: "Send",
    recording: "Recording...",
    stopRecording: "Stop Recording",
    startRecording: "Start Recording",
    uploadImage: "Upload Image",
    requestAgent: "Request Human Agent",
    agentRequested: "I've requested a human agent to assist you. Please wait while we connect you to an available agent.",
    typing: "AI is typing...",
    minimize: "Minimize",
    maximize: "Maximize",
    close: "Close",
    newMessage: "New message",
    imagePrompt: "What's in this image?",
    voiceNotSupported: "Voice recording is not supported in your browser.",
    voicePermissionDenied: "Microphone permission denied. Please allow microphone access to use voice recording.",
    voiceError: "Voice recording error. Please try again.",
    uploadError: "Upload failed. Please try again.",
    connectionError: "Connection error. Please check your internet connection.",
    sessionError: "Session error. Please refresh the page.",
    dragDropText: "Drop your image here",
    chatTitle: "Halal Assistant",
    chatSubtitle: "AI-powered Halal guidance",
    sources: "Sources",
    handoverRequested: "Agent requested",
    handoverCompleted: "Connected to agent",
    agentJoined: "Agent joined the conversation",
    sessionCompleted: "Session completed",
  },
  'ms-MY': {
    welcome: "Helo! Saya adalah pembantu AI anda untuk pertanyaan Halal. Bagaimana saya boleh membantu anda hari ini?\n\n💬 Anda juga boleh menghubungi saya di WhatsApp untuk respons yang lebih pantas!",
    placeholder: "Taip mesej anda...",
    send: "Hantar",
    recording: "Merakam...",
    stopRecording: "Berhenti Merakam",
    startRecording: "Mula Merakam",
    uploadImage: "Muat Naik Imej",
    requestAgent: "Minta Ejen Manusia",
    agentRequested: "Saya telah meminta ejen manusia untuk membantu anda. Sila tunggu sementara kami menghubungkan anda dengan ejen yang tersedia.",
    typing: "AI sedang menaip...",
    minimize: "Kecilkan",
    maximize: "Besarkan",
    close: "Tutup",
    newMessage: "Mesej baru",
    imagePrompt: "Apa yang ada dalam imej ini?",
    voiceNotSupported: "Rakaman suara tidak disokong dalam pelayar anda.",
    voicePermissionDenied: "Kebenaran mikrofon ditolak. Sila benarkan akses mikrofon untuk menggunakan rakaman suara.",
    voiceError: "Ralat rakaman suara. Sila cuba lagi.",
    uploadError: "Muat naik gagal. Sila cuba lagi.",
    connectionError: "Ralat sambungan. Sila periksa sambungan internet anda.",
    sessionError: "Ralat sesi. Sila muat semula halaman.",
    dragDropText: "Lepaskan imej anda di sini",
    chatTitle: "Pembantu Halal",
    chatSubtitle: "Panduan Halal berkuasa AI",
    sources: "Sumber",
    handoverRequested: "Ejen diminta",
    handoverCompleted: "Disambungkan kepada ejen",
    agentJoined: "Ejen menyertai perbualan",
    sessionCompleted: "Sesi selesai",
  },
};

export type Language = keyof typeof translations;

export function createTranslationFunction(language: Language = 'en') {
  return (key: keyof typeof translations.en): string => {
    return translations[language]?.[key] || translations.en[key] || key;
  };
}

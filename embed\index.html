<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Halal Chat Widget - Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example-content {
            margin-bottom: 40px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .config-section {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4fd;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Halal Chat Widget - Integration Example</h1>
        
        <div class="example-content">
            <h2>About This Widget</h2>
            <p>This is a standalone embeddable chat widget that provides AI-powered Halal knowledge assistance. 
               It can be integrated into any website without conflicting with existing styles or frameworks.</p>
            
            <h3>Features</h3>
            <ul>
                <li>✅ Voice input (push-to-talk)</li>
                <li>✅ Text messaging</li>
                <li>✅ Image upload</li>
                <li>✅ Markdown rendering</li>
                <li>✅ Auto-scroll to latest messages</li>
                <li>✅ Maximize/minimize functionality</li>
                <li>✅ Complete style isolation</li>
                <li>✅ Cross-origin compatible</li>
                <li>🆕 <strong>AI-powered form assistance</strong></li>
            </ul>

            <h3>🤖 Form Integration Demo</h3>
            <p>Experience the new AI-powered form assistance feature:</p>
            <div style="margin: 15px 0; padding: 15px; background: #f0f8ff; border: 1px solid #4CAF50; border-radius: 8px;">
                <p><strong>🎯 Try the Interactive Form Demo:</strong></p>
                <p>See how the AI can help users complete forms through natural language commands like "Fill my <NAME_EMAIL>" or "What fields are still empty?"</p>
                <p style="margin-top: 15px;">
                    <a href="public/sample.html" style="display: inline-block; background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                        🚀 Open Form Demo
                    </a>
                    <a href="examples/form-integration.html" style="display: inline-block; background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold; margin-left: 10px;">
                        📖 Integration Example
                    </a>
                </p>
            </div>

            <div class="config-section">
                <h3>Current Configuration</h3>
                <p><strong>Base URL:</strong> <span id="current-base-url">Loading...</span></p>
                <p><strong>Bot Slug:</strong> <span id="current-bot-slug">Loading...</span></p>
            </div>

            <h3>Integration Code</h3>
            <p>Add this code to your website to embed the chat widget:</p>
            
            <div class="code-block">
&lt;!-- Add this script tag to your HTML --&gt;
&lt;script src="https://your-domain.com/halal-chat-widget.js"&gt;&lt;/script&gt;

&lt;!-- Initialize the widget --&gt;
&lt;script&gt;
  HalalChatWidget.init({
    baseURL: 'https://your-api-server.com',
    botSlug: 'your-bot-slug', // optional
    position: 'bottom-right', // optional: bottom-right, bottom-left
    theme: 'light', // optional: light, dark
    enableFormTools: true // optional: enable AI form assistance
  });
&lt;/script&gt;
            </div>

            <h3>Configuration Options</h3>
            <ul>
                <li><strong>baseURL</strong> (required): The URL of your Halal chat API server</li>
                <li><strong>botSlug</strong> (optional): Specific bot to use for conversations</li>
                <li><strong>position</strong> (optional): Widget position - 'bottom-right' or 'bottom-left'</li>
                <li><strong>theme</strong> (optional): Color theme - 'light' or 'dark'</li>
                <li><strong>enableFormTools</strong> (optional): Enable AI form interaction capabilities - true or false</li>
            </ul>
        </div>
    </div>

    <!-- The chat widget will be initialized here -->
    <script type="module" src="/src/index.tsx"></script>
    <script>
        // Update the configuration display
        document.addEventListener('DOMContentLoaded', function() {
            // Example configuration
            const config = {
                baseURL: 'http://localhost:16001', // Change this to your API server
                botSlug: 'halal-assistant', // Optional: specific bot
                position: 'bottom-right',
                theme: 'light',
                language: 'en'
            };

            // Display current config
            document.getElementById('current-base-url').textContent = config.baseURL;
            document.getElementById('current-bot-slug').textContent = config.botSlug || 'Default bot';

            // Initialize the widget (this will be available after the script loads)
            if (window.HalalChatWidget) {
                window.HalalChatWidget.init(config);
            } else {
                // Wait for the script to load
                window.addEventListener('load', function() {
                    if (window.HalalChatWidget) {
                        window.HalalChatWidget.init(config);
                    }
                });
            }
        });
    </script>
</body>
</html>

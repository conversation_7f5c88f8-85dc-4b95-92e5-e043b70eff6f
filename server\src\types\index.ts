export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'agent' | 'tool'
  content: string
  timestamp?: Date
  imageUrl?: string
  audioUrl?: string
  fileUrl?: string
  fileName?: string
  agentId?: number
  agentName?: string
}

export interface ChatSession {
  id: string
  messages: ChatMessage[]
  createdAt: Date
  userId?: string
  platform?: string
  platformId?: string
  status?: string
  isHandedOver?: boolean
  lastMessageAt?: Date
  assignedAgent?: AgentUser
  botId?: number
}

export interface OpenAIResponse {
  success: boolean
  message?: string
  error?: string
  usage?:
    | {
        prompt_tokens: number
        completion_tokens: number
        total_tokens: number
      }
    | undefined
  toolCalls?: ToolCall[]
  finishReason?: 'stop' | 'length' | 'tool_calls' | 'content_filter'
}

// Tool calling types
export interface ToolCall {
  id: string
  type: 'function'
  function: {
    name: string
    arguments: string
  }
}

export interface ToolCallResult {
  toolCallId: string
  result: string | object | null
  success: boolean
  error?: string
}

// Twilio WhatsApp API Types

// Runtime configuration used by TwilioService instance
export interface TwilioConfig {
  accountSid: string
  authToken: string // Note: In DB, this might be stored encrypted or referenced via a secrets manager
  phoneNumber: string // Twilio WhatsApp number, e.g., whatsapp:+***********
  webhookUrl?: string // Optional webhook URL if configured
}

// Representation of Twilio configuration as stored in the database
export interface TwilioDbConfig extends TwilioConfig {
  id: number
  siteId: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface TwilioWebhookPayload {
  SmsMessageSid: string
  MessageSid: string
  SmsSid: string
  AccountSid: string
  MessagingServiceSid?: string
  From: string // User's number, e.g., whatsapp:+**********
  To: string // Your Twilio number, e.g., whatsapp:+***********
  Body?: string
  NumMedia?: string // Number of media items, as a string
  MediaContentType0?: string
  MediaUrl0?: string
  // ... MediaContentTypeN, MediaUrlN for N < NumMedia
  [key: string]: any // For any other properties Twilio might send
}

export interface TwilioMessage {
  id: string // Usually MessageSid from Twilio
  from: string // User's WhatsApp number
  to: string // Your Twilio WhatsApp number
  type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'unknown'
  content: string // Text content or caption
  mediaUrl?: string
  mediaContentType?: string
  timestamp: Date // Recommended to use arrival time as Twilio payload doesn't always provide it directly
  originalPayload: TwilioWebhookPayload // Store the original payload
  sessionId?: string // Optional: if you want to link it to a session
}

// Representation of a Twilio message as stored in the database
export interface TwilioDbMessage {
  id: string // Twilio Message SID (PK)
  siteId: number
  accountSid?: string | null // Associated Twilio Account SID
  from: string // Sender's number (e.g., whatsapp:+**********)
  to: string // Your Twilio number (e.g., whatsapp:+***********)
  body?: string | null // Message text content
  type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'unknown' // Message type
  mediaUrl?: string | null
  mediaContentType?: string | null
  status?: string | null // e.g., sent, delivered, read, failed, received
  direction: 'inbound' | 'outbound'
  errorCode?: number | null
  errorMessage?: string | null
  timestamp: Date // Timestamp of the message event
  sessionId?: string | null // Link to chat_sessions.id
  createdAt?: Date // Record creation timestamp
  updatedAt?: Date // Record update timestamp
}

export interface TwilioSendMessageResponse {
  success: boolean
  messageSid?: string // SID of the message created on Twilio
  error?: string
  errorCode?: number // Twilio error code
  moreInfo?: string // URL to more info about the error
}

export interface FunctionDefinition {
  name: string
  description: string
  parameters: {
    type: 'object'
    properties: Record<
      string,
      {
        type: string
        description?: string
        enum?: string[]
        items?: object
      }
    >
    required?: string[]
  }
}

export interface ToolDefinition {
  type: 'function'
  function: FunctionDefinition
}

export interface MessageWithToolCalls extends Omit<ChatMessage, 'content'> {
  content: string | null
  toolCalls?: ToolCall[]
}

export interface ToolMessage extends ChatMessage {
  role: 'tool'
  toolCallId: string
}

// Message handling configuration
export interface MessageHandlerConfig {
  maxMessageHistory: number
  maxToolCallIterations: number
  defaultModel: string
  temperature?: number
  enableToolCalling: boolean
  platform: 'whatsapp' | 'facebook' | 'web' | 'twilio'
}

// Environment configuration for Cloudflare Workers
export interface WorkerEnv {
  OPENAI_API_KEY?: string
  R2R_BASE_URL?: string
  R2R_API_KEY?: string
  DATABASE_URL?: string
  [key: string]: string | undefined
}

export interface ConsolidatedMessageRequest {
  message: string
  sessionId: string
  platform: string
  messageType?: 'text' | 'image' | 'audio' | 'document'
  mediaUrl?: string
  userId: string
  config?: Partial<MessageHandlerConfig>
  systemPrompt?: string
  botId?: number
  botSlug?: string
}

export interface ConsolidatedMessageResponse {
  success: boolean
  message?: string
  error?: string
  usage?:
    | {
        prompt_tokens: number
        completion_tokens: number
        total_tokens: number
      }
    | undefined
  toolCallsExecuted?: number
  sessionId: string
}

export interface TranscriptionResponse {
  success: boolean
  text?: string
  error?: string
}

export interface TextToSpeechResponse {
  success: boolean
  audioBuffer?: Buffer
  error?: string
}

export interface UploadedFile {
  fieldname: string
  originalname: string
  encoding: string
  mimetype: string
  destination: string
  filename: string
  path: string
  size: number
}

export interface FileUploadResponse {
  type: 'image' | 'audio'
  url?: string
  filename?: string
  originalFilename: string
  size?: number
  mimetype?: string
  transcription?: string | undefined
}

export interface ChatMessageRequest {
  sessionId: string
  message: string
  model?: string
}

export interface ImageAnalysisRequest {
  sessionId: string
  imageUrl: string
  prompt?: string
  model?: string
}

// R2R Search Types
export interface TextResult {
  text: string
  type: 'vector' | 'graph' | string
  document_id?: string | null
  score: number
  wordCount: number
  metadata?: Record<string, unknown>
  content?: {
    description?: string
    summary?: string
  }
  result_type?: string
  resultType?: string
  documentId?: string
}

export interface ParseR2rOptions {
  retrieveDocument: boolean
  maxWordCount: number
  includeGraph: boolean
  minScore: number
  limit: number
}

export interface R2rSearchResult {
  texts: TextResult[]
  totalWordCount: number
}

export interface SearchResponse {
  query: string
  results: TextResult[]
  totalChunks: number
  totalGraphResults: number
  options: ParseR2rOptions
}

export interface ChatResponse {
  message: string
  sessionId: string
  usage?:
    | {
        prompt_tokens: number
        completion_tokens: number
        total_tokens: number
      }
    | undefined
}

export interface ErrorResponse {
  success: boolean
  error: string
  message?: string
  statusCode?: number
  timestamp?: string
  requestId?: string
}

// WhatsApp Business API Types
export interface WhatsAppConfig {
  id?: number
  accessToken: string
  phoneNumberId: string
  webhookVerifyToken: string
  businessAccountId?: string
  isActive: boolean
  createdAt?: Date
  updatedAt?: Date
}

export interface WhatsAppMessage {
  id: string
  from: string
  to: string
  type: 'text' | 'image' | 'audio' | 'document'
  content: string
  mediaUrl?: string
  timestamp: Date
  sessionId?: string
}

export interface WhatsAppWebhookPayload {
  object: string
  entry: Array<{
    id: string
    changes: Array<{
      value: {
        messaging_product: string
        metadata: {
          display_phone_number: string
          phone_number_id: string
        }
        contacts?: Array<{
          profile: {
            name: string
          }
          wa_id: string
        }>
        messages?: Array<{
          from: string
          id: string
          timestamp: string
          text?: {
            body: string
          }
          type: string
          image?: {
            id: string
            mime_type: string
            sha256: string
          }
          audio?: {
            id: string
            mime_type: string
            sha256: string
          }
        }>
        statuses?: Array<{
          id: string
          status: string
          timestamp: string
          recipient_id: string
        }>
      }
      field: string
    }>
  }>
}

export interface WhatsAppSendMessageRequest {
  to: string
  type: 'text' | 'image'
  text?: {
    body: string
  }
  image?: {
    link: string
    caption?: string
  }
}

export interface WhatsAppSendMessageResponse {
  success: boolean
  messageId?: string
  error?: string
}

// Admin Authentication Types
export interface AdminUser {
  id: number
  username: string
  passwordHash: string
  createdAt: Date
  lastLoginAt?: Date
}

export interface AdminLoginRequest {
  username: string
  password: string
}

export interface AdminLoginResponse {
  success: boolean
  token?: string
  error?: string
  user?: {
    id: number
    username: string
    email?: string
    firstName?: string
    lastName?: string
    roles: UserRole[]
    isActive: boolean
    isOnline: boolean
    lastSeenAt?: Date
  }
}

export enum UserRole {
  SUPERADMIN = 'SUPERADMIN',
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  AGENT = 'AGENT',
  SUPERVISOR = 'SUPERVISOR',
}

// Unified User type for internal database operations
export interface User {
  id: number
  username: string
  email?: string | null
  passwordHash: string
  firstName?: string | null
  lastName?: string | null
  roles: UserRole[] // Array of roles for multi-role support
  isActive: boolean
  isOnline: boolean
  lastSeenAt?: Date | null
  lastLoginAt?: Date | null
  createdAt: Date
  updatedAt: Date
}

// For API responses, excluding sensitive data like passwordHash
export interface UserResponse {
  id: number
  username: string
  email?: string | null
  firstName?: string | null
  lastName?: string | null
  roles: UserRole[] // Array of roles
  isActive: boolean
  isOnline: boolean
  lastSeenAt?: Date | null
  lastLoginAt?: Date | null
  createdAt: Date
  updatedAt: Date
}

// Legacy AdminUser interface for backward compatibility
export interface AdminUser extends User {}

// Legacy AdminUserResponse interface for backward compatibility
export interface AdminUserResponse extends UserResponse {}

export interface AdminUserCreationRequest {
  username: string
  password: string // Password required for creation
  email?: string
  firstName?: string
  lastName?: string
  roles: UserRole[] // Multiple roles supported
  isActive?: boolean
}

export interface AdminUserUpdateRequest {
  username?: string
  password?: string // Password optional for update
  email?: string
  firstName?: string
  lastName?: string
  roles?: UserRole[] // Multiple roles supported
  isActive?: boolean
}

// User creation request for unified interface
export interface UserCreationRequest {
  username: string
  email?: string
  password: string
  firstName?: string
  lastName?: string
  roles: UserRole[] // Multiple roles supported
  isActive?: boolean
}

// User update request for unified interface
export interface UserUpdateRequest {
  username?: string
  email?: string
  password?: string
  firstName?: string
  lastName?: string
  roles?: UserRole[] // Multiple roles supported
  isActive?: boolean
}

export interface JWTPayload {
  userId: number
  username: string
  role: UserRole // Use the enum
  iat: number
  exp: number
}

// Contact Management Types
export interface Contact {
  id: number
  siteId: number
  name: string
  title?: string | null
  department?: string | null
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email: string
  phone?: string | null
  mobile?: string | null
  fax?: string | null
  address?: string | null
  city?: string | null
  state?: string | null
  postcode?: string | null
  country?: string | null
  website?: string | null
  workingHours?: string | null
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface ContactCreationRequest {
  name: string
  title?: string
  department?: string
  type: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email: string
  phone?: string
  mobile?: string
  fax?: string
  address?: string
  city?: string
  state?: string
  postcode?: string
  country?: string
  website?: string
  workingHours?: string
  isActive?: boolean
}

export interface ContactUpdateRequest {
  name?: string
  title?: string
  department?: string
  type?: 'general' | 'support' | 'sales' | 'technical' | 'emergency'
  email?: string
  phone?: string
  mobile?: string
  fax?: string
  address?: string
  city?: string
  state?: string
  postcode?: string
  country?: string
  website?: string
  workingHours?: string
  isActive?: boolean
}

// S3Configuration Types
export type S3ConfigurationPreset =
  | 'aws_s3'
  | 'cloudflare_r2'
  | 'digitalocean_spaces'
  | 'other'

export interface S3Configuration {
  id?: number
  serviceName: string
  accessKeyId: string
  secretAccessKey: string
  bucketName: string
  region?: string | null
  endpointUrl?: string | null
  createdAt?: Date
  updatedAt?: Date
}

export interface S3ConfigurationCreationRequest
  extends Omit<S3Configuration, 'id' | 'createdAt' | 'updatedAt'> {}
export interface S3ConfigurationUpdateRequest
  extends Partial<S3ConfigurationCreationRequest> {}

// Collection Types
export enum CollectionStatus {
  ACTIVE = 'ACTIVE',
  DISABLED = 'DISABLED',
}

export interface Collection {
  id?: number
  name: string
  status: CollectionStatus
  // documents will be handled by relation, not explicitly in this type for basic CRUD
  createdAt?: Date
  updatedAt?: Date
}

export interface CollectionCreationRequest
  extends Omit<Collection, 'id' | 'createdAt' | 'updatedAt'> {}
export interface CollectionUpdateRequest
  extends Partial<CollectionCreationRequest> {}

// Document Types
export interface Document {
  id?: number
  collectionId: number
  s3ConfigurationId: number
  s3Key: string
  filename: string
  filesize?: number | null
  mimetype?: string | null
  createdAt?: Date
  updatedAt?: Date
  // For display, we might want to join S3Configuration details or Collection name
  collectionName?: string // Example of a joined field for display
  s3ConfigurationServiceName?: string // Example of a joined field for display
}

export interface DocumentCreationRequest
  extends Omit<
    Document,
    | 'id'
    | 'createdAt'
    | 'updatedAt'
    | 'collectionName'
    | 's3ConfigurationServiceName'
  > {}

// For listing, we might want pagination
export interface PaginatedDocumentsResponse {
  documents: Document[]
  total: number
  page: number
  limit: number
}

export interface HealthResponse {
  status: string
  timestamp: string
}

// Facebook Messenger API Types
export interface FacebookConfig {
  id?: number
  siteId: number
  pageAccessToken: string
  pageId: string
  appSecret: string
  verifyToken: string
  isActive: boolean
  createdAt?: Date
  updatedAt?: Date
}

// Service Types
export enum ServiceType {
  R2R_RAG = 'R2R_RAG',
  SMTP_PROVIDER = 'SMTP_PROVIDER',
  EXTERNAL_API = 'EXTERNAL_API',
}

// Base Service Configuration
export interface ServiceConfiguration {
  id?: number
  siteId: number
  name: string
  type: ServiceType
  description?: string
  isActive: boolean
  configuration: string // JSON string for flexible config storage
  createdAt?: Date
  updatedAt?: Date
}

// R2R RAG Configuration
export interface R2RConfiguration {
  url: string
  username?: string
  password?: string
  collectionId?: string
  searchLimit?: number
  minScore?: number
}

// SMTP Provider Configuration
export interface SMTPConfiguration {
  host: string
  port: number
  secure: boolean // true for 465, false for other ports
  username: string
  password: string
  fromEmail: string
  fromName?: string
}

// External API Configuration
export interface ExternalAPIConfiguration {
  baseUrl: string
  apiKey?: string
  headers?: Record<string, string>
  timeout?: number
  retryAttempts?: number
}

// Service Creation Request
export interface ServiceCreationRequest {
  name: string
  type: ServiceType
  description?: string
  isActive?: boolean
  configuration: R2RConfiguration | SMTPConfiguration | ExternalAPIConfiguration
}

// Service Update Request
export interface ServiceUpdateRequest {
  name?: string
  description?: string
  isActive?: boolean
  configuration?:
    | R2RConfiguration
    | SMTPConfiguration
    | ExternalAPIConfiguration
}

export interface FacebookMessage {
  id: string
  from: string
  to: string
  type: 'text' | 'image' | 'audio' | 'file' | 'postback' | 'quick_reply'
  content: string
  mediaUrl?: string
  timestamp: Date
  sessionId?: string
}

export interface FacebookWebhookPayload {
  object: string
  entry: Array<{
    id: string
    time: number
    messaging: Array<{
      sender: {
        id: string
      }
      recipient: {
        id: string
      }
      timestamp: number
      message?: {
        mid: string
        text?: string
        is_echo?: boolean
        attachments?: Array<{
          type: 'image' | 'audio' | 'video' | 'file'
          payload: {
            url: string
          }
        }>
        quick_reply?: {
          payload: string
        }
      }
      postback?: {
        title: string
        payload: string
      }
      delivery?: {
        mids: string[]
        watermark: number
      }
      read?: {
        watermark: number
      }
    }>
  }>
}

export interface FacebookSendMessageRequest {
  recipient: {
    id: string
  }
  message: {
    text?: string
    attachment?: {
      type: 'image' | 'audio' | 'video' | 'file'
      payload: {
        url: string
        is_reusable?: boolean
      }
    }
    quick_replies?: Array<{
      content_type: 'text'
      title: string
      payload: string
    }>
  }
  messaging_type?: 'RESPONSE' | 'UPDATE' | 'MESSAGE_TAG'
}

export interface FacebookSendMessageResponse {
  success: boolean
  messageId?: string
  error?: string
}

export interface FacebookUserProfile {
  id: string
  first_name?: string
  last_name?: string
  profile_pic?: string
  locale?: string
  timezone?: number
  gender?: string
}

export interface SessionResponse {
  sessionId?: string
  error?: string
  message?: string
}

// Agent Support Types - Legacy interface for backward compatibility
export interface AgentUser {
  id: number
  username: string
  email: string
  passwordHash?: string // Include for authentication purposes
  firstName: string
  lastName: string
  role: 'agent' | 'supervisor'
  isActive: boolean
  isOnline: boolean
  lastSeenAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface SessionAssignment {
  id: number
  sessionId: string
  agentId: number
  assignedAt: Date
  status: 'active' | 'completed' | 'transferred'
  completedAt?: Date
  agent?: User // Updated to use unified User type
}

export interface HandoverRequest {
  id: number
  sessionId: string
  requestedBy: 'user' | 'bot' | 'admin'
  reason?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'pending' | 'assigned' | 'completed' | 'cancelled'
  assignedTo?: number
  requestedAt: Date
  assignedAt?: Date
  completedAt?: Date
  agent?: AgentUser
}

export interface AgentLoginRequest {
  username: string
  password: string
}

export interface AgentLoginResponse {
  success: boolean
  token?: string
  agent?: AgentUser
  error?: string
}

export interface AgentJWTPayload {
  agentId: number
  username: string
  role: string
  iat: number
  exp: number
}

export interface ChatSessionWithDetails extends ChatSession {
  messageCount: number
  lastMessage?: ChatMessage
  handoverRequest?: HandoverRequest
  assignment?: SessionAssignment
}

export interface AgentDashboardStats {
  totalSessions: number
  activeSessions: number
  pendingHandovers: number
  completedToday: number
}

export interface SessionHandoverRequest {
  sessionId: string
  reason?: string
  priority?: 'low' | 'normal' | 'high' | 'urgent'
}

export interface SessionHandoverResponse {
  success: boolean
  handoverRequestId?: number
  error?: string
}

export interface AgentMessageRequest {
  sessionId: string
  content: string
  type?: 'text' | 'voice' | 'file'
  fileUrl?: string
  fileName?: string
  audioUrl?: string
}

export interface AgentMessageResponse {
  success: boolean
  messageId?: string
  error?: string
}

// Halal Knowledge Search Types
export interface HalalKnowledgeRequest {
  query: string
  sessionId?: string
  maxResults?: number
  minScore?: number
  includeContext?: boolean
}

export interface HalalKnowledgeResponse {
  success: boolean
  answer?: string
  sources?: TextResult[]
  query: string
  sessionId?: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
  error?: string
}

// Bot Types
export interface Bot {
  id: number
  siteId: number
  name: string
  slug: string
  provider: string
  model: string
  temperature: number
  isDefault: boolean
  systemPrompt?: string | null
  createdAt: Date
  updatedAt: Date
}

export interface BotCreationRequest {
  siteId: number
  name: string
  slug: string
  provider: string
  model: string
  temperature?: number
  isDefault?: boolean
  systemPrompt?: string
}

export interface BotUpdateRequest {
  name?: string
  slug?: string
  provider?: string
  model?: string
  temperature?: number
  isDefault?: boolean
  systemPrompt?: string
}

export interface BotWithUsage extends Bot {
  totalUsage?: {
    totalMessages: number
    totalInputTokens: number
    totalOutputTokens: number
  }
}

// Team Types
export interface Team {
  id: number
  siteId: number
  name: string
  description?: string | null
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface TeamCreationRequest {
  siteId: number
  name: string
  description?: string
  isActive?: boolean
}

export interface TeamUpdateRequest {
  name?: string
  description?: string
  isActive?: boolean
}

export interface TeamWithMembers extends Team {
  userCount?: number
  botCount?: number
  users?: UserResponse[]
  bots?: Bot[]
}

// Agent Team Assignment Types
export interface AgentTeam {
  id: number
  agentId: number
  teamId: number
  assignedAt: Date
  assignedBy?: number | null
  createdAt: Date
  updatedAt: Date
}

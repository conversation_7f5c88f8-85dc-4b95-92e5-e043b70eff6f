# Deployment Guide

This guide explains how to build and deploy the Halal Chat Widget for production use.

## Building for Production

### 1. Install Dependencies

```bash
npm install
```

### 2. Build the Widget

```bash
npm run build
```

This creates the production files in the `dist/` directory:
- `halal-chat-widget.js` - The main widget file
- `example.html` - Example integration page

### 3. Test the Build

```bash
npm run serve
```

This starts a local server to test the built widget.

## Deployment Options

### Option 1: CDN Deployment

Upload `halal-chat-widget.js` to your CDN and reference it:

```html
<script src="https://your-cdn.com/halal-chat-widget.js"></script>
<script>
  HalalChatWidget.init({
    baseURL: 'https://your-api-server.com'
  });
</script>
```

### Option 2: Self-Hosted

1. Upload `halal-chat-widget.js` to your web server
2. Reference it from your HTML:

```html
<script src="/path/to/halal-chat-widget.js"></script>
```

### Option 3: NPM Package (Future)

The widget could be published as an NPM package for easier integration:

```bash
npm install halal-chat-widget
```

## Server Requirements

### API Server Setup

Ensure your API server supports the following endpoints:

1. **Create Session**
   ```
   POST /api/chat/session
   ```

2. **Send Message**
   ```
   POST /api/chat/message
   POST /api/sites/{siteId}/bots/{botSlug}/chat/message
   ```

3. **Send Image**
   ```
   POST /api/chat/image
   POST /api/sites/{siteId}/bots/{botSlug}/chat/image
   ```

4. **Upload Files**
   ```
   POST /api/upload
   ```

5. **Bot Configuration**
   ```
   GET /api/sites/{siteId}/bots/slug/{botSlug}
   ```

### CORS Configuration

Configure CORS headers on your API server to allow requests from domains hosting the widget:

```javascript
// Express.js example
app.use(cors({
  origin: [
    'https://your-website.com',
    'https://client-website.com',
    // Add all domains that will use the widget
  ],
  credentials: true
}));
```

### Security Considerations

1. **Rate Limiting**: Implement rate limiting on API endpoints
2. **Input Validation**: Validate all inputs on the server side
3. **File Upload Security**: Scan uploaded files for malware
4. **API Authentication**: Consider implementing API keys for widget usage

## Environment Configuration

### Production Environment Variables

Set these on your API server:

```env
# Required
OPENAI_API_KEY=your_openai_api_key
DATABASE_URL=your_database_url

# Optional
DEFAULT_SITE_ID=1
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
```

### Widget Configuration

Configure the widget for production:

```javascript
HalalChatWidget.init({
  baseURL: 'https://api.halalportal.com', // Production API URL
  botSlug: 'halal-assistant',
  position: 'bottom-right',
  theme: 'light',
  language: 'en'
});
```

## Performance Optimization

### 1. File Size Optimization

The built widget is already optimized, but you can further reduce size by:

- Enabling gzip compression on your server
- Using a CDN with compression
- Implementing HTTP/2 for faster loading

### 2. Caching

Set appropriate cache headers for the widget file:

```
Cache-Control: public, max-age=31536000
```

### 3. Loading Strategy

For better performance, load the widget asynchronously:

```html
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://your-cdn.com/halal-chat-widget.js';
    script.async = true;
    script.onload = function() {
      HalalChatWidget.init({
        baseURL: 'https://your-api-server.com'
      });
    };
    document.head.appendChild(script);
  })();
</script>
```

## Monitoring and Analytics

### 1. Error Tracking

Monitor JavaScript errors in the widget:

```javascript
window.addEventListener('error', function(e) {
  // Send error to your analytics service
  console.error('Widget error:', e.error);
});
```

### 2. Usage Analytics

Track widget usage:

```javascript
// Track widget initialization
HalalChatWidget.init({
  baseURL: 'https://your-api-server.com',
  onInit: function() {
    // Track initialization
    analytics.track('chat_widget_initialized');
  }
});
```

### 3. API Monitoring

Monitor your API endpoints for:
- Response times
- Error rates
- Usage patterns
- Rate limit violations

## Troubleshooting

### Common Issues

1. **Widget not loading**
   - Check script URL
   - Verify CORS configuration
   - Check browser console for errors

2. **API requests failing**
   - Verify baseURL configuration
   - Check CORS headers
   - Ensure API endpoints are accessible

3. **Voice recording not working**
   - Ensure HTTPS (required for microphone access)
   - Check browser compatibility
   - Verify user permissions

### Debug Mode

Enable debug logging by adding to localStorage:

```javascript
localStorage.setItem('halal-chat-debug', 'true');
```

## Updates and Versioning

### Updating the Widget

1. Build new version
2. Upload to CDN/server
3. Update version in your integration
4. Test thoroughly before deploying

### Version Management

Consider versioning your widget files:

```
halal-chat-widget-v1.0.0.js
halal-chat-widget-v1.1.0.js
```

This allows gradual rollouts and easy rollbacks.

## Support

For technical support or questions about deployment:

1. Check the README.md for basic setup
2. Review the examples in the `examples/` directory
3. Check the browser console for error messages
4. Verify API server logs for backend issues

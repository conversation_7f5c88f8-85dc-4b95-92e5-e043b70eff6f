'use client'

import { UserRole } from '@/types'

interface UserStatusBadgeProps {
  role?: UserRole
  isActive?: boolean
  isOnline?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function UserStatusBadge({
  role,
  isActive,
  isOnline,
  size = 'sm',
}: UserStatusBadgeProps) {
  const sizeClasses = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  }

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'bg-purple-100 text-purple-800'
      case UserRole.ADMIN:
        return 'bg-blue-100 text-blue-800'
      case UserRole.EDITOR:
        return 'bg-green-100 text-green-800'
      case UserRole.AGENT:
        return 'bg-yellow-100 text-yellow-800'
      case UserRole.SUPERVISOR:
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="flex items-center space-x-1">
      {role && (
        <span
          className={`inline-flex items-center rounded-full font-medium ${getRoleColor(role)} ${sizeClasses[size]}`}
        >
          {role}
        </span>
      )}

      {isActive !== undefined && (
        <span
          className={`inline-flex items-center rounded-full font-medium ${sizeClasses[size]} ${
            isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}
        >
          {isActive ? 'Active' : 'Inactive'}
        </span>
      )}

      {isOnline && (
        <span
          className={`inline-flex items-center rounded-full bg-blue-100 font-medium text-blue-800 ${sizeClasses[size]}`}
        >
          Online
        </span>
      )}
    </div>
  )
}

interface RoleBadgeProps {
  role: UserRole
  size?: 'sm' | 'md' | 'lg'
}

export function RoleBadge({ role, size = 'sm' }: RoleBadgeProps) {
  return <UserStatusBadge role={role} size={size} />
}

interface StatusBadgeProps {
  isActive: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function StatusBadge({ isActive, size = 'sm' }: StatusBadgeProps) {
  return <UserStatusBadge isActive={isActive} size={size} />
}

interface OnlineBadgeProps {
  isOnline: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function OnlineBadge({ isOnline, size = 'sm' }: OnlineBadgeProps) {
  return isOnline ? <UserStatusBadge isOnline={isOnline} size={size} /> : null
}

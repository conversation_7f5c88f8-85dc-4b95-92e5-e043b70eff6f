'use client'

export const runtime = 'edge'

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  ArrowLeft,
  CheckCircle,
  Clock,
  MessageSquare,
  RefreshCw,
  Send,
  User,
} from 'lucide-react'
import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAdminAuthGuard } from '@/hooks/useAuthGuard'
import { Link } from '@/i18n/navigation'
import { api } from '@/lib/api'
import { UserRole } from '@/types/roles'

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic'

interface ChatMessage {
  id: string
  role: 'system' | 'user' | 'assistant' | 'agent'
  content: string
  timestamp: string
  agentId?: number
  agentName?: string
  imageUrl?: string
  audioUrl?: string
  fileUrl?: string
  fileName?: string
}

interface ChatSession {
  id: string
  userId?: string
  platform: string
  platformId?: string
  status: string
  isHandedOver: boolean
  createdAt: string
  lastMessageAt?: string
  messages: ChatMessage[]
  agentId?: number
  agentName?: string
}

export default function AdminSessionDetailPage() {
  const params = useParams()
  const router = useRouter()
  const sessionId = params.id as string
  const { user, loading } = useAdminAuthGuard([
    UserRole.ADMIN,
    UserRole.AGENT,
    UserRole.SUPERVISOR,
  ])

  const [session, setSession] = useState<ChatSession | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [isSending, setIsSending] = useState(false)

  const fetchSession = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await api.admin.getSession(sessionId)
      if (response.success && response.data) {
        setSession(response.data)
      } else {
        setError('Session not found')
      }
    } catch (err) {
      console.error('Error fetching session:', err)
      setError('Failed to load session')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (!loading && user && sessionId) {
      fetchSession()
    }
  }, [loading, user, sessionId])

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return

    try {
      setIsSending(true)
      const response = await api.admin.sendMessage(sessionId, {
        content: newMessage,
        type: 'text',
      })

      if (response.success) {
        setNewMessage('')
        // Refresh session to get updated messages
        await fetchSession()
      } else {
        setError('Failed to send message')
      }
    } catch (err) {
      console.error('Error sending message:', err)
      setError('Failed to send message')
    } finally {
      setIsSending(false)
    }
  }

  const handleCompleteSession = async () => {
    try {
      const response = await api.admin.completeSession(sessionId)
      if (response.success) {
        // Refresh session to get updated status
        await fetchSession()
      } else {
        setError('Failed to complete session')
      }
    } catch (err) {
      console.error('Error completing session:', err)
      setError('Failed to complete session')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getSenderIcon = (role: string) => {
    switch (role) {
      case 'user':
        return <User className="h-4 w-4 text-blue-600" />
      case 'agent':
        return <User className="h-4 w-4 text-green-600" />
      case 'assistant':
        return <MessageSquare className="h-4 w-4 text-purple-600" />
      case 'system':
        return <MessageSquare className="h-4 w-4 text-gray-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (error && !session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin/sessions"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Sessions
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link
                href="/admin/sessions"
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <MessageSquare className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Session: {sessionId}
                </h1>
                {session && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      session.platform === 'facebook'
                        ? 'bg-blue-100 text-blue-800'
                        : session.platform === 'whatsapp'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {session.platform}
                    </span>
                    <span>•</span>
                    <span>{session.platformId || session.userId || 'Anonymous'}</span>
                    {session.isHandedOver && (
                      <>
                        <span>•</span>
                        <span className="text-orange-600 font-medium">Handed Over</span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {session && (
                <>
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(session.status)}`}
                  >
                    {session.status}
                  </span>
                  {session.status === 'active' && (
                    <button
                      onClick={handleCompleteSession}
                      className="flex items-center px-4 py-2 text-sm text-white bg-green-600 hover:bg-green-700 rounded-md"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Complete Session
                    </button>
                  )}
                </>
              )}
              <button
                onClick={fetchSession}
                disabled={isLoading}
                className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md disabled:opacity-50"
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
                />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {session && (
          <div className="bg-white shadow rounded-lg">
            {/* Session Info */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Created</p>
                  <p className="text-sm text-gray-900">
                    {formatDate(session.createdAt)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Last Activity
                  </p>
                  <p className="text-sm text-gray-900">
                    {session.lastMessageAt
                      ? formatDate(session.lastMessageAt)
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Messages</p>
                  <p className="text-sm text-gray-900">
                    {session.messages?.length || 0}
                  </p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Conversation
              </h3>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {session.messages && session.messages.length > 0 ? (
                  session.messages
                    .filter(message => message.role !== 'system') // Hide system messages
                    .map(message => (
                      <div
                        key={message.id}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            message.role === 'user'
                              ? 'bg-blue-600 text-white'
                              : message.role === 'agent'
                                ? 'bg-green-100 text-green-900'
                                : message.role === 'assistant'
                                  ? 'bg-purple-100 text-purple-900'
                                  : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          <div className="flex items-center space-x-2 mb-1">
                            {getSenderIcon(message.role)}
                            <span className="text-xs font-medium">
                              {message.role === 'agent' && message.agentName
                                ? message.agentName
                                : message.role === 'assistant'
                                  ? 'AI Assistant'
                                  : message.role === 'user'
                                    ? 'User'
                                    : message.role}
                            </span>
                            <span className="text-xs opacity-75">
                              {formatDate(message.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm">{message.content}</p>
                          {message.imageUrl && (
                            <img
                              src={message.imageUrl}
                              alt="Message attachment"
                              className="mt-2 max-w-full h-auto rounded"
                            />
                          )}
                          {message.fileUrl && (
                            <a
                              href={message.fileUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="mt-2 inline-block text-xs underline"
                            >
                              {message.fileName || 'Download file'}
                            </a>
                          )}
                        </div>
                      </div>
                    ))
                ) : (
                  <div className="text-center py-8">
                    <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No messages yet
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      This session hasn't started yet.
                    </p>
                  </div>
                )}
              </div>

              {/* Send Message */}
              {session.status === 'active' && (
                <div className="mt-6 border-t border-gray-200 pt-4">
                  <div className="flex space-x-4">
                    <div className="flex-1">
                      <textarea
                        value={newMessage}
                        onChange={e => setNewMessage(e.target.value)}
                        placeholder="Type your message..."
                        rows={3}
                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        onKeyDown={e => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault()
                            handleSendMessage()
                          }
                        }}
                      />
                    </div>
                    <button
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim() || isSending}
                      className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Send className="h-4 w-4 mr-2" />
                      {isSending ? 'Sending...' : 'Send'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

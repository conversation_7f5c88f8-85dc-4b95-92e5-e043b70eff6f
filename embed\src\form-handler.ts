/**
 * Form Handler Library
 * Provides DOM querying and form interaction capabilities similar to Puppeteer
 * Shared between embed and front applications
 */

export interface ElementInfo {
  tagName: string;
  id?: string;
  name?: string;
  className?: string;
  type?: string;
  value?: string;
  checked?: boolean;
  selected?: boolean;
  textContent?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  readonly?: boolean;
}

export interface QueryResult {
  found: boolean;
  count: number;
  elements: ElementInfo[];
  error?: string;
}

export interface ActionResult {
  success: boolean;
  message: string;
  error?: string;
  value?: any;
}

export class FormHandler {
  /**
   * Query elements by various selectors
   */
  static queryElements(selector: string): QueryResult {
    try {
      const elements = document.querySelectorAll(selector);
      
      return {
        found: elements.length > 0,
        count: elements.length,
        elements: Array.from(elements).map(el => this.getElementInfo(el as HTMLElement))
      };
    } catch (error) {
      return {
        found: false,
        count: 0,
        elements: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Query element by ID
   */
  static getElementById(id: string): QueryResult {
    return this.queryElements(`#${CSS.escape(id)}`);
  }

  /**
   * Query elements by name attribute
   */
  static getElementsByName(name: string): QueryResult {
    return this.queryElements(`[name="${CSS.escape(name)}"]`);
  }

  /**
   * Query elements by class name
   */
  static getElementsByClass(className: string): QueryResult {
    return this.queryElements(`.${CSS.escape(className)}`);
  }

  /**
   * Query elements by tag name
   */
  static getElementsByTag(tagName: string): QueryResult {
    return this.queryElements(tagName);
  }

  /**
   * Query elements by XPath (simplified implementation)
   */
  static getElementsByXPath(xpath: string): QueryResult {
    try {
      const result = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      );
      
      const elements: ElementInfo[] = [];
      for (let i = 0; i < result.snapshotLength; i++) {
        const node = result.snapshotItem(i);
        if (node && node.nodeType === Node.ELEMENT_NODE) {
          elements.push(this.getElementInfo(node as HTMLElement));
        }
      }
      
      return {
        found: elements.length > 0,
        count: elements.length,
        elements
      };
    } catch (error) {
      return {
        found: false,
        count: 0,
        elements: [],
        error: error instanceof Error ? error.message : 'Invalid XPath'
      };
    }
  }

  /**
   * Fill a form field with a value
   */
  static fillField(selector: string, value: string): ActionResult {
    try {
      const element = document.querySelector(selector) as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
      
      if (!element) {
        return {
          success: false,
          message: `Element not found: ${selector}`
        };
      }

      if (element.disabled || element.readOnly) {
        return {
          success: false,
          message: `Element is disabled or readonly: ${selector}`
        };
      }

      // Handle different input types
      if (element instanceof HTMLInputElement) {
        switch (element.type) {
          case 'checkbox':
          case 'radio':
            element.checked = value.toLowerCase() === 'true' || value === '1';
            break;
          case 'file':
            return {
              success: false,
              message: 'File inputs cannot be programmatically filled for security reasons'
            };
          default:
            element.value = value;
        }
      } else if (element instanceof HTMLTextAreaElement) {
        element.value = value;
      } else if (element instanceof HTMLSelectElement) {
        element.value = value;
      }

      // Trigger change event
      element.dispatchEvent(new Event('change', { bubbles: true }));
      element.dispatchEvent(new Event('input', { bubbles: true }));

      return {
        success: true,
        message: `Successfully filled field: ${selector}`,
        value: element.value || element.checked
      };
    } catch (error) {
      return {
        success: false,
        message: `Error filling field: ${selector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Select option(s) in a dropdown or multi-select
   */
  static selectOption(selector: string, values: string | string[]): ActionResult {
    try {
      const element = document.querySelector(selector) as HTMLSelectElement;
      
      if (!element || element.tagName !== 'SELECT') {
        return {
          success: false,
          message: `Select element not found: ${selector}`
        };
      }

      if (element.disabled) {
        return {
          success: false,
          message: `Select element is disabled: ${selector}`
        };
      }

      const valueArray = Array.isArray(values) ? values : [values];
      let selectedCount = 0;

      // Clear existing selections if not multiple
      if (!element.multiple) {
        Array.from(element.options).forEach(option => option.selected = false);
      }

      // Select the specified options
      Array.from(element.options).forEach(option => {
        if (valueArray.includes(option.value) || valueArray.includes(option.text)) {
          option.selected = true;
          selectedCount++;
        }
      });

      // Trigger change event
      element.dispatchEvent(new Event('change', { bubbles: true }));

      return {
        success: selectedCount > 0,
        message: `Selected ${selectedCount} option(s) in: ${selector}`,
        value: element.multiple ? 
          Array.from(element.selectedOptions).map(opt => opt.value) : 
          element.value
      };
    } catch (error) {
      return {
        success: false,
        message: `Error selecting option: ${selector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Click an element
   */
  static clickElement(selector: string): ActionResult {
    try {
      const element = document.querySelector(selector) as HTMLElement;
      
      if (!element) {
        return {
          success: false,
          message: `Element not found: ${selector}`
        };
      }

      if (element instanceof HTMLInputElement && element.disabled) {
        return {
          success: false,
          message: `Element is disabled: ${selector}`
        };
      }

      element.click();

      return {
        success: true,
        message: `Successfully clicked: ${selector}`
      };
    } catch (error) {
      return {
        success: false,
        message: `Error clicking element: ${selector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get current value of a form field
   */
  static getFieldValue(selector: string): ActionResult {
    try {
      const element = document.querySelector(selector) as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
      
      if (!element) {
        return {
          success: false,
          message: `Element not found: ${selector}`
        };
      }

      let value: any;
      
      if (element instanceof HTMLInputElement) {
        switch (element.type) {
          case 'checkbox':
          case 'radio':
            value = element.checked;
            break;
          default:
            value = element.value;
        }
      } else if (element instanceof HTMLSelectElement) {
        value = element.multiple ? 
          Array.from(element.selectedOptions).map(opt => opt.value) : 
          element.value;
      } else {
        value = element.value;
      }

      return {
        success: true,
        message: `Retrieved value from: ${selector}`,
        value
      };
    } catch (error) {
      return {
        success: false,
        message: `Error getting field value: ${selector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get all form data from a form
   */
  static getFormData(formSelector: string): ActionResult {
    try {
      const form = document.querySelector(formSelector) as HTMLFormElement;
      
      if (!form || form.tagName !== 'FORM') {
        return {
          success: false,
          message: `Form not found: ${formSelector}`
        };
      }

      const formData = new FormData(form);
      const data: Record<string, any> = {};

      // Convert FormData to plain object
      for (const [key, value] of formData.entries()) {
        if (data[key]) {
          // Handle multiple values (like checkboxes with same name)
          if (Array.isArray(data[key])) {
            data[key].push(value);
          } else {
            data[key] = [data[key], value];
          }
        } else {
          data[key] = value;
        }
      }

      return {
        success: true,
        message: `Retrieved form data from: ${formSelector}`,
        value: data
      };
    } catch (error) {
      return {
        success: false,
        message: `Error getting form data: ${formSelector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate form and get validation status
   */
  static validateForm(formSelector: string): ActionResult {
    try {
      const form = document.querySelector(formSelector) as HTMLFormElement;
      
      if (!form || form.tagName !== 'FORM') {
        return {
          success: false,
          message: `Form not found: ${formSelector}`
        };
      }

      const isValid = form.checkValidity();
      const invalidFields: string[] = [];

      // Find invalid fields
      const formElements = form.querySelectorAll('input, select, textarea');
      formElements.forEach(element => {
        const el = element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
        if (!el.checkValidity()) {
          const identifier = el.id || el.name || el.className || el.tagName.toLowerCase();
          invalidFields.push(identifier);
        }
      });

      return {
        success: true,
        message: `Form validation completed for: ${formSelector}`,
        value: {
          isValid,
          invalidFields,
          invalidCount: invalidFields.length
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Error validating form: ${formSelector}`,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get detailed information about an element
   */
  private static getElementInfo(element: HTMLElement): ElementInfo {
    const info: ElementInfo = {
      tagName: element.tagName.toLowerCase(),
      textContent: element.textContent?.trim() || undefined
    };

    if (element.id) info.id = element.id;
    if (element.getAttribute('name')) info.name = element.getAttribute('name')!;
    if (element.className) info.className = element.className;

    if (element instanceof HTMLInputElement) {
      info.type = element.type;
      info.value = element.value;
      info.checked = element.checked;
      info.placeholder = element.placeholder || undefined;
      info.required = element.required;
      info.disabled = element.disabled;
      info.readonly = element.readOnly;
    } else if (element instanceof HTMLTextAreaElement) {
      info.value = element.value;
      info.placeholder = element.placeholder || undefined;
      info.required = element.required;
      info.disabled = element.disabled;
      info.readonly = element.readOnly;
    } else if (element instanceof HTMLSelectElement) {
      info.value = element.value;
      info.required = element.required;
      info.disabled = element.disabled;
    } else if (element instanceof HTMLOptionElement) {
      info.value = element.value;
      info.selected = element.selected;
    }

    return info;
  }
}

export default FormHandler;

'use client'

import { Label } from '@/components/ui/label'
import { UserRole } from '@/types'

interface RoleSelectorProps {
  selectedRoles: UserRole[]
  onRoleToggle: (role: UserRole) => void
  label?: string
  required?: boolean
  error?: string
}

export function RoleSelector({
  selectedRoles,
  onRoleToggle,
  label = 'User Roles',
  required = true,
  error,
}: RoleSelectorProps) {
  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'bg-purple-100 text-purple-800 border-purple-300'
      case UserRole.ADMIN:
        return 'bg-blue-100 text-blue-800 border-blue-300'
      case UserRole.EDITOR:
        return 'bg-green-100 text-green-800 border-green-300'
      case UserRole.AGENT:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case UserRole.SUPERVISOR:
        return 'bg-orange-100 text-orange-800 border-orange-300'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300'
    }
  }

  const getRoleDescription = (role: UserRole) => {
    switch (role) {
      case UserRole.SUPERADMIN:
        return 'Full system access and site management'
      case UserRole.ADMIN:
        return 'Administrative access to site features'
      case UserRole.EDITOR:
        return 'Content editing and management'
      case UserRole.AGENT:
        return 'Customer support and chat handling'
      case UserRole.SUPERVISOR:
        return 'Agent supervision and management'
      default:
        return ''
    }
  }

  return (
    <div className="space-y-3">
      <Label>
        {label} {required && '*'}
      </Label>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {Object.values(UserRole).map(role => (
          <button
            key={role}
            type="button"
            onClick={() => onRoleToggle(role)}
            className={`rounded-lg border p-3 text-left transition-all hover:shadow-sm ${
              selectedRoles.includes(role)
                ? getRoleColor(role) + ' ring-2 ring-blue-500 ring-offset-2'
                : 'border-gray-300 bg-gray-50 text-gray-700 hover:bg-gray-100'
            }`}
          >
            <div className="text-sm font-medium">{role}</div>
            <div className="mt-1 text-xs opacity-75">
              {getRoleDescription(role)}
            </div>
          </button>
        ))}
      </div>
      {selectedRoles.length === 0 && required && (
        <p className="text-sm text-red-600">
          At least one role must be selected
        </p>
      )}
      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  )
}

// Team Types for Frontend
export interface Team {
  id: number
  siteId: number
  name: string
  description?: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface TeamWithMembers extends Team {
  userCount?: number
  botCount?: number
  users?: Array<{
    id: number
    username: string
    email?: string | null
    firstName?: string | null
    lastName?: string | null
    isActive: boolean
    isOnline: boolean
  }>
  bots?: Array<{
    id: number
    name: string
    slug: string
    provider: string
    model: string
    isActive: boolean
    isDefault: boolean
  }>
}

// Session filtering by team
export interface SessionTeamFilter {
  teamId?: number | null
  teamName?: string | null
}

// Enhanced session interfaces with team information
export interface SessionWithTeam {
  id: string
  userId?: string
  platform: string
  platformId?: string
  status: string
  isHandedOver: boolean
  createdAt: string
  lastMessageAt?: string
  agentId?: number
  agentName?: string
  agentTeamId?: number | null
  agentTeamName?: string | null
  botId?: number
  botName?: string
  botTeamId?: number | null
  botTeamName?: string | null
}

// Team filter options for dashboard
export interface TeamFilterOptions {
  showAllTeams: boolean
  selectedTeamId: number | null
  availableTeams: Team[]
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chat Widget</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="debug-info">
        <h1>Chat Widget Debug Page</h1>
        <p>This page helps debug the chat widget initialization.</p>
        
        <div id="status-container">
            <div class="status info">Initializing...</div>
        </div>
        
        <button onclick="testAPI()">Test API Connection</button>
        <button onclick="testWidget()">Test Widget</button>
        
        <div id="test-results"></div>
    </div>

    <!-- Chat Widget -->
    <script src="../dist/halal-chat-widget.umd.js"></script>
    <script>
        const statusContainer = document.getElementById('status-container');
        const testResults = document.getElementById('test-results');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            statusContainer.appendChild(div);
        }
        
        function addTestResult(message) {
            const div = document.createElement('div');
            div.innerHTML = `<pre>${message}</pre>`;
            testResults.appendChild(div);
        }
        
        // Check if widget is loaded
        if (typeof HalalChatWidget !== 'undefined') {
            addStatus('✅ HalalChatWidget loaded successfully', 'success');
            
            try {
                // Initialize the widget
                HalalChatWidget.init({
                    baseURL: 'http://localhost:16001',
                    position: 'bottom-right',
                    theme: 'light',
                    language: 'en',
                    enableFormTools: true
                });
                addStatus('✅ Widget initialized successfully', 'success');
            } catch (error) {
                addStatus('❌ Widget initialization failed: ' + error.message, 'error');
            }
        } else {
            addStatus('❌ HalalChatWidget not found', 'error');
        }
        
        async function testAPI() {
            try {
                addTestResult('Testing API connection...');
                
                // Test health endpoint
                const healthResponse = await fetch('http://localhost:16001/health');
                addTestResult(`Health check: ${healthResponse.status} ${healthResponse.statusText}`);
                
                // Test session creation
                const sessionResponse = await fetch('http://localhost:16001/api/chat/session', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                const sessionData = await sessionResponse.json();
                addTestResult(`Session creation: ${JSON.stringify(sessionData)}`);
                
                // Test message sending
                if (sessionData.sessionId) {
                    const messageResponse = await fetch('http://localhost:16001/api/chat/message', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            sessionId: sessionData.sessionId,
                            message: 'Hello, this is a test message'
                        })
                    });
                    const messageData = await messageResponse.json();
                    addTestResult(`Message response: ${JSON.stringify(messageData)}`);
                }
                
            } catch (error) {
                addTestResult(`API test failed: ${error.message}`);
            }
        }
        
        function testWidget() {
            if (typeof HalalChatWidget !== 'undefined') {
                addTestResult('Widget methods available:');
                addTestResult(`- init: ${typeof HalalChatWidget.init}`);
                addTestResult(`- open: ${typeof HalalChatWidget.open}`);
                addTestResult(`- close: ${typeof HalalChatWidget.close}`);
                addTestResult(`- isOpen: ${typeof HalalChatWidget.isOpen}`);
                addTestResult(`- destroy: ${typeof HalalChatWidget.destroy}`);
                
                // Check if widget container exists
                const container = document.getElementById('halal-chat-widget-container');
                if (container) {
                    addTestResult('✅ Widget container found in DOM');
                    addTestResult(`Container style: ${container.style.cssText}`);
                } else {
                    addTestResult('❌ Widget container not found in DOM');
                }
            } else {
                addTestResult('❌ HalalChatWidget not available');
            }
        }
        
        // Auto-run tests after page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testWidget();
            }, 1000);
        });
    </script>
</body>
</html>

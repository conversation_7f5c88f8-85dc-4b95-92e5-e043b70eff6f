'use client';

export const runtime = 'edge';

import { ArrowLeft, Edit, Upload, Plus } from 'lucide-react';
import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Link, useRouter } from '@/i18n/navigation';
import { useCollectionsStore } from '@/stores/collections';
import { useDocumentsStore } from '@/stores/documents';
import { useAdminAuthGuard } from '@/hooks/useAuthGuard';
import { createColumns } from '../../documents/columns';
import { DocumentsDataTable } from '../../documents/data-table';

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

interface CollectionDetailPageProps {
  params: {
    id: string;
  };
}

export default function CollectionDetailPage({ params }: CollectionDetailPageProps) {
  const router = useRouter();
  const collectionId = parseInt(params.id, 10);

  const { currentCollection, isLoading: collectionsLoading, error: collectionsError, fetchCollectionById, clearError: clearCollectionsError } = useCollectionsStore();
  const { documents, isLoading: documentsLoading, error: documentsError, fetchDocuments, clearError: clearDocumentsError } = useDocumentsStore();

  // Auth guard
  useAdminAuthGuard();

  useEffect(() => {
    if (!isNaN(collectionId)) {
      fetchCollectionById(collectionId);
      fetchDocuments(collectionId);
    }
  }, [collectionId, fetchCollectionById, fetchDocuments]);

  // Create columns with refresh callback
  const columns = createColumns(() => {
    fetchDocuments(collectionId); // Refresh the documents list after deletion
  });

  if (isNaN(collectionId)) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Invalid Collection ID</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">The collection ID provided is not valid.</p>
            <Link href="/admin/collections">
              <Button>Back to Collections</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (collectionsError || documentsError) {
    const error = collectionsError || documentsError;
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button onClick={() => {
                fetchCollectionById(collectionId);
                fetchDocuments(collectionId);
              }}>
                Try Again
              </Button>
              <Button variant="outline" onClick={() => {
                clearCollectionsError();
                clearDocumentsError();
              }}>
                Clear Error
              </Button>
              <Link href="/admin/collections">
                <Button variant="outline">Back to Collections</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (collectionsLoading || !currentCollection) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/collections">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Collections
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold tracking-tight">{currentCollection.name}</h1>
          <p className="text-gray-600">Collection details and documents</p>
        </div>
        <div className="flex gap-2">
          <Link href={`/admin/collections/${collectionId}/upload`}>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          </Link>
          <Link href={`/admin/collections/${collectionId}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit Collection
            </Button>
          </Link>
        </div>
      </div>

      {/* Collection Info */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Collection Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  currentCollection.status === 'ACTIVE'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {currentCollection.status}
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Documents</p>
              <p className="text-lg font-semibold">{currentCollection._count?.documents || 0}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Created</p>
              <p className="text-lg font-semibold">
                {new Date(currentCollection.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Documents</CardTitle>
              <CardDescription>
                Documents in this collection
              </CardDescription>
            </div>
            <Link href={`/admin/collections/${collectionId}/upload`}>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Document
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {documentsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500 text-lg">No documents in this collection</p>
              <p className="text-gray-400 mt-1">Upload your first document to get started</p>
              <Link href={`/admin/collections/${collectionId}/upload`}>
                <Button className="mt-4">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </Button>
              </Link>
            </div>
          ) : (
            <DocumentsDataTable columns={columns} data={documents} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}

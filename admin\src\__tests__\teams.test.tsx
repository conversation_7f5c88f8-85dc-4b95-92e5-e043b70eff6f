import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useTeamsStore } from '@/stores/teams'
import type { Team, TeamCreateRequest } from '@/types'

// Mock the auth store
jest.mock('@/stores/auth', () => ({
  useAuthStore: {
    getState: () => ({ token: 'mock-token' }),
  },
}))

// Mock fetch
global.fetch = jest.fn()

describe('Teams Store', () => {
  beforeEach(() => {
    // Reset the store state
    useTeamsStore.setState({
      teams: [],
      currentTeam: null,
      isLoading: false,
      error: null,
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      },
    })
    
    // Clear all mocks
    jest.clearAllMocks()
  })

  describe('fetchTeams', () => {
    it('should fetch teams successfully', async () => {
      const mockTeams: Team[] = [
        {
          id: 1,
          siteId: 1,
          name: 'Test Team',
          description: 'A test team',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ]

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: mockTeams,
        }),
      })

      const { fetchTeams } = useTeamsStore.getState()
      await fetchTeams()

      const state = useTeamsStore.getState()
      expect(state.teams).toEqual(mockTeams)
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
    })

    it('should handle fetch error', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        statusText: 'Internal Server Error',
      })

      const { fetchTeams } = useTeamsStore.getState()
      await fetchTeams()

      const state = useTeamsStore.getState()
      expect(state.teams).toEqual([])
      expect(state.isLoading).toBe(false)
      expect(state.error).toContain('Failed to fetch teams')
    })
  })

  describe('createTeam', () => {
    it('should create team successfully', async () => {
      const teamData: TeamCreateRequest = {
        name: 'New Team',
        description: 'A new team',
        isActive: true,
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { id: 1, ...teamData },
        }),
      })

      const { createTeam } = useTeamsStore.getState()
      const result = await createTeam(teamData)

      expect(result).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin/teams'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-token',
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(teamData),
        })
      )
    })

    it('should handle create error', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        statusText: 'Bad Request',
      })

      const { createTeam } = useTeamsStore.getState()
      const result = await createTeam({
        name: 'Test Team',
        isActive: true,
      })

      expect(result).toBe(false)
      const state = useTeamsStore.getState()
      expect(state.error).toContain('Failed to create team')
    })
  })

  describe('updateTeam', () => {
    it('should update team successfully', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { id: 1, name: 'Updated Team' },
        }),
      })

      const { updateTeam } = useTeamsStore.getState()
      const result = await updateTeam(1, { name: 'Updated Team' })

      expect(result).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin/teams/1'),
        expect.objectContaining({
          method: 'PUT',
        })
      )
    })
  })

  describe('deleteTeam', () => {
    it('should delete team successfully', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: { message: 'Team deleted successfully' },
        }),
      })

      const { deleteTeam } = useTeamsStore.getState()
      const result = await deleteTeam(1)

      expect(result).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/admin/teams/1'),
        expect.objectContaining({
          method: 'DELETE',
        })
      )
    })
  })
})

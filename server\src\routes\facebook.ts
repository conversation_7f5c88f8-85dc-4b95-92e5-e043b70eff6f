import express, { type Request, type Response } from 'express'
import { v4 as uuidv4 } from 'uuid'
import facebookService from '../services/facebook'
import openaiService from '../services/openai'
import DatabaseService from '../services/database'
import type { ChatMessage, ChatSession, FacebookWebhookPayload } from '../types'

const router: express.Router = express.Router()

// Store Facebook chat sessions in memory (in production, use a database)
const facebookSessions = new Map<string, ChatSession>()

// Initialize database service
const databaseService = new DatabaseService()

// Webhook verification endpoint
router.get('/webhook', (req: Request, res: Response): void => {
  const mode = req.query['hub.mode']
  const token = req.query['hub.verify_token']
  const challenge = req.query['hub.challenge']

  console.log('Facebook webhook verification request:', {
    mode,
    token,
    challenge,
  })

  if (mode && token && challenge) {
    const verificationResult = facebookService.verifyWebhookChallenge(
      mode as string,
      token as string,
      challenge as string,
    )

    if (verificationResult) {
      console.log('Facebook webhook verified successfully')
      res.status(200).send(challenge)
      return
    }
  }

  console.log('Facebook webhook verification failed')
  res.status(403).send('Forbidden')
})

// Webhook endpoint for receiving Facebook messages
router.post('/webhook', async (req: Request, res: Response): Promise<void> => {
  try {
    const signature = req.headers['x-hub-signature-256'] as string
    const payload = JSON.stringify(req.body)

    // Verify webhook signature for security
    if (!facebookService.verifyWebhookSignature(payload, signature)) {
      console.log('Facebook webhook signature verification failed')
      res.status(403).send('Forbidden')
      return
    }

    const webhookPayload: FacebookWebhookPayload = req.body

    // Process each entry in the webhook payload
    for (const entry of webhookPayload.entry) {
      for (const messagingEvent of entry.messaging) {
        if (messagingEvent.message && !messagingEvent.message.is_echo) {
          // Handle incoming message
          await handleIncomingMessage(messagingEvent)
        } else if (messagingEvent.postback) {
          // Handle postback (button clicks, etc.)
          await handlePostback(messagingEvent)
        }
      }
    }

    res.status(200).send('OK')
  } catch (error) {
    console.error('Facebook webhook error:', error)
    res.status(500).send('Internal Server Error')
  }
})

// Handle incoming Facebook message and integrate with OpenAI
async function handleIncomingMessage(messagingEvent: any): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const message = messagingEvent.message

    let messageText = ''
    let messageType = 'text'
    let mediaUrl: string | undefined

    if (message.text) {
      messageText = message.text
      messageType = 'text'
    } else if (message.attachments && message.attachments.length > 0) {
      const attachment = message.attachments[0]
      messageType = attachment.type
      mediaUrl = attachment.payload.url
      messageText = `[${attachment.type.toUpperCase()} received]`
    } else if (message.quick_reply) {
      messageText = message.quick_reply.payload
      messageType = 'quick_reply'
    }

    console.log(`Processing Facebook message from ${senderId}: ${messageText}`)

    // Get or create session for this sender
    let session = facebookSessions.get(senderId)
    if (!session) {
      const sessionId = uuidv4()
      session = {
        id: sessionId,
        messages: [
          {
            role: 'system',
            content:
              'You are a helpful assistant for Halal inquiries. You speak in Bahasa Malaysia by default. You may switch between Bahasa Malaysia, Chinese, English, and Tamil. Keep responses concise for Facebook Messenger.',
          },
        ],
        createdAt: new Date(),
        platform: 'facebook',
        platformId: senderId,
        status: 'active',
        isHandedOver: false,
      }
      facebookSessions.set(senderId, session)

      // Create session in database
      try {
        await databaseService.createChatSession(
          sessionId,
          'facebook',
          senderId,
          undefined, // userId
          undefined  // botId
        )
        console.log(`Created Facebook session in database: ${sessionId}`)
      } catch (error) {
        console.error('Error creating Facebook session in database:', error)
      }
    }

    // Add user message to session
    const userMessage: ChatMessage = {
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl: mediaUrl,
    }

    session.messages.push(userMessage)

    // Check for handover request keywords
    const handoverKeywords = ['agent', 'human', 'speak to agent', 'talk to human', 'customer service', 'help me', 'complaint']
    const shouldRequestHandover = handoverKeywords.some(keyword =>
      messageText.toLowerCase().includes(keyword.toLowerCase())
    )

    // Log user message to database
    try {
      // Add to chat_messages table
      await databaseService.addChatMessage(
        session.id,
        'user',
        messageText,
        undefined, // agentId
        mediaUrl,  // imageUrl
        undefined, // audioUrl
        undefined, // fileUrl
        undefined  // fileName
      )

      // Also log to facebook_messages table
      const messageId = message.mid || `fb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      await databaseService.logFacebookMessage(
        messageId,
        senderId,
        facebookService.getConfig()?.pageId || 'page',
        messageType,
        messageText,
        'inbound',
        mediaUrl,
        session.id
      )
      console.log(`Logged Facebook user message: ${messageId}`)
    } catch (error) {
      console.error('Error logging Facebook user message:', error)
    }

    // Check if session is already handed over to an agent
    if (session.isHandedOver) {
      // Session is with an agent, just log the message and notify agent
      console.log(`Message from ${senderId} in handed-over session ${session.id}: ${messageText}`)

      // Send acknowledgment to user
      await facebookService.sendTextMessage(
        senderId,
        'Your message has been received by our customer service team. An agent will respond to you shortly.'
      )

      // The message is already logged above, so we're done
      return
    }

    // Handle handover request if triggered
    if (shouldRequestHandover) {
      try {
        const handoverRequestId = await databaseService.createHandoverRequest(
          session.id,
          'user',
          `User requested human assistance via Facebook. Message: "${messageText}"`,
          'normal'
        )

        // Update session handover status in memory
        session.isHandedOver = true

        console.log(`Created handover request ${handoverRequestId} for Facebook session ${session.id}`)

        // Send handover confirmation message
        await facebookService.sendTextMessage(
          senderId,
          'I understand you need to speak with a human agent. I\'ve forwarded your request to our customer service team. An agent will be with you shortly.'
        )

        // Don't process with AI if handover is requested
        return
      } catch (error) {
        console.error('Error creating handover request:', error)
        // Continue with AI response if handover fails
      }
    }

    // Get response from OpenAI
    let openaiResponse

    if (messageType === 'image' && mediaUrl) {
      // Use image analysis for image messages
      openaiResponse = await openaiService.analyzeImage(
        mediaUrl,
        messageText ||
          "What's in this image? Please provide a brief description.",
      )
    } else {
      // Prepare messages for OpenAI (exclude timestamps)
      const openaiMessages = session.messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }))

      // Use text chat for text messages
      openaiResponse = await openaiService.sendTextMessage(openaiMessages)
    }

    if (openaiResponse.success && openaiResponse.message) {
      // Add assistant message to session
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: openaiResponse.message,
        timestamp: new Date(),
      }
      session.messages.push(assistantMessage)

      // Send response back to Facebook Messenger
      const sendResult = await facebookService.sendTextMessage(
        senderId,
        openaiResponse.message,
      )

      if (sendResult.success) {
        // Log assistant message to database
        try {
          // Add to chat_messages table
          await databaseService.addChatMessage(
            session.id,
            'assistant',
            openaiResponse.message,
            undefined, // agentId
            undefined, // imageUrl
            undefined, // audioUrl
            undefined, // fileUrl
            undefined  // fileName
          )

          // Also log to facebook_messages table
          const outboundMessageId = sendResult.messageId || `fb_out_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          await databaseService.logFacebookMessage(
            outboundMessageId,
            facebookService.getConfig()?.pageId || 'page',
            senderId,
            'text',
            openaiResponse.message,
            'outbound',
            undefined,
            session.id
          )
          console.log(`Logged Facebook assistant message: ${outboundMessageId}`)
        } catch (error) {
          console.error('Error logging Facebook assistant message:', error)
        }
      } else {
        console.error('Failed to send Facebook message:', sendResult.error)
      }
    } else {
      console.error('OpenAI request failed:', openaiResponse.error)

      // Send error message to user
      await facebookService.sendTextMessage(
        senderId,
        'Sorry, I encountered an error processing your message. Please try again later.',
      )
    }
  } catch (error) {
    console.error('Error handling Facebook message:', error)
  }
}

// Handle Facebook postback events (button clicks, etc.)
async function handlePostback(messagingEvent: any): Promise<void> {
  try {
    const senderId = messagingEvent.sender.id
    const postback = messagingEvent.postback

    console.log(
      `Processing Facebook postback from ${senderId}: ${postback.payload}`,
    )

    // Handle different postback payloads
    switch (postback.payload) {
      case 'GET_STARTED':
        await facebookService.sendTextMessage(
          senderId,
          'Welcome to Halal Malaysia! I can help you with halal certification inquiries. How can I assist you today?',
        )
        break

      case 'HELP':
        await facebookService.sendTextMessage(
          senderId,
          'I can help you with:\n• Halal certification information\n• Application procedures\n• Requirements and guidelines\n• General halal inquiries\n\nJust ask me anything!',
        )
        break

      default:
        // Treat unknown postbacks as regular messages
        await handleIncomingMessage({
          sender: { id: senderId },
          message: { text: postback.payload },
        })
        break
    }
  } catch (error) {
    console.error('Error handling Facebook postback:', error)
  }
}

export default router

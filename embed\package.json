{"name": "halal-chat-widget", "version": "1.0.0", "description": "Standalone embeddable chat widget for Halal knowledge assistance", "main": "dist/halal-chat-widget.umd.js", "module": "dist/halal-chat-widget.es.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "build:watch": "vite build --watch", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["chat", "widget", "embeddable", "halal", "ai"], "author": "Halal Portal", "license": "MIT", "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "typescript": "^5.2.0", "vite": "^5.0.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.0"}}
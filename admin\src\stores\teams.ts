import { create } from 'zustand'
import type {
  PaginatedResponse,
  SearchParams,
  Team,
  TeamCreateRequest,
  TeamUpdateRequest,
  TeamWithMembers,
} from '@/types'
import { useAuthStore } from './auth'

interface TeamsState {
  // State
  teams: Team[]
  currentTeam: TeamWithMembers | null
  isLoading: boolean
  error: string | null
  pagination: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
}

interface TeamsActions {
  // Actions
  fetchTeams: (params?: SearchParams) => Promise<void>
  fetchTeamById: (id: number) => Promise<TeamWithMembers | null>
  createTeam: (data: TeamCreateRequest) => Promise<boolean>
  updateTeam: (id: number, data: TeamUpdateRequest) => Promise<boolean>
  deleteTeam: (id: number) => Promise<boolean>
  setCurrentTeam: (team: TeamWithMembers | null) => void
  clearError: () => void
}

type TeamsStore = TeamsState & TeamsActions

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

export const useTeamsStore = create<TeamsStore>((set, get) => ({
  // Initial state
  teams: [],
  currentTeam: null,
  isLoading: false,
  error: null,
  pagination: {
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0,
  },

  // Actions
  fetchTeams: async (params?: SearchParams) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const queryParams = new URLSearchParams()
      if (params?.query) queryParams.append('search', params.query)
      if (params?.page) queryParams.append('page', params.page.toString())
      if (params?.limit) queryParams.append('limit', params.limit.toString())
      if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)

      const response = await fetch(
        `${API_BASE_URL}/api/admin/teams?${queryParams}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to fetch teams: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch teams')
      }

      // Handle both paginated and simple array responses
      if (Array.isArray(result.data)) {
        set({
          teams: result.data,
          pagination: {
            total: result.data.length,
            page: 1,
            limit: result.data.length,
            totalPages: 1,
          },
          isLoading: false,
        })
      } else {
        set({
          teams: result.data.items || [],
          pagination: {
            total: result.data.total || 0,
            page: result.data.page || 1,
            limit: result.data.limit || 10,
            totalPages: result.data.totalPages || 0,
          },
          isLoading: false,
        })
      }
    } catch (error) {
      console.error('Error fetching teams:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch teams',
        isLoading: false,
      })
    }
  },

  fetchTeamById: async (id: number) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch team: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch team')
      }

      const team = result.data as TeamWithMembers
      set({ currentTeam: team, isLoading: false })
      return team
    } catch (error) {
      console.error('Error fetching team:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch team',
        isLoading: false,
      })
      return null
    }
  },

  createTeam: async (data: TeamCreateRequest) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to create team: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to create team')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error creating team:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to create team',
        isLoading: false,
      })
      return false
    }
  },

  updateTeam: async (id: number, data: TeamUpdateRequest) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams/${id}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to update team: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to update team')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error updating team:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update team',
        isLoading: false,
      })
      return false
    }
  },

  deleteTeam: async (id: number) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/teams/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to delete team: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete team')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error deleting team:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete team',
        isLoading: false,
      })
      return false
    }
  },

  setCurrentTeam: (team: TeamWithMembers | null) => {
    set({ currentTeam: team })
  },

  clearError: () => {
    set({ error: null })
  },
}))

# Halal Chat Widget

A standalone embeddable chat widget that provides AI-powered Halal knowledge assistance. This widget can be integrated into any website without conflicting with existing styles or frameworks.

## Features

- ✅ **Voice Input**: Push-to-talk functionality with automatic transcription
- ✅ **Text Messaging**: Real-time chat with AI assistant
- ✅ **Image Upload**: Drag-and-drop image upload with analysis
- ✅ **Markdown Rendering**: Rich text formatting in responses
- ✅ **Auto-scroll**: Automatic scrolling to latest messages
- ✅ **Maximize/Minimize**: Expandable chat interface
- ✅ **Complete Style Isolation**: No conflicts with host website styles
- ✅ **Cross-origin Compatible**: Works across different domains
- ✅ **Multi-language Support**: English and Bahasa Malaysia
- ✅ **Bot Configuration**: Support for multiple bot configurations

## Quick Start

### 1. Include the Script

Add this script tag to your HTML:

```html
<script src="https://your-domain.com/halal-chat-widget.umd.js"></script>
```

### 2. Initialize the Widget

```html
<script>
  HalalChatWidget.init({
    baseURL: 'https://your-api-server.com',
    botSlug: 'your-bot-slug', // optional
    position: 'bottom-right', // optional: bottom-right, bottom-left
    theme: 'light', // optional: light, dark
    language: 'en' // optional: en, ms-MY
  });
</script>
```

### 3. Alternative: Data Attributes

You can also configure the widget using data attributes:

```html
<script
  src="https://your-domain.com/halal-chat-widget.umd.js"
  data-base-url="https://your-api-server.com"
  data-bot-slug="your-bot-slug"
  data-position="bottom-right"
  data-theme="light"
  data-language="en">
</script>
```

## Configuration Options

| Option | Type | Required | Default | Description |
|--------|------|----------|---------|-------------|
| `baseURL` | string | Yes | - | The URL of your Halal chat API server |
| `botSlug` | string | No | - | Specific bot to use for conversations |
| `position` | string | No | `'bottom-right'` | Widget position: `'bottom-right'` or `'bottom-left'` |
| `theme` | string | No | `'light'` | Color theme: `'light'` or `'dark'` |
| `language` | string | No | `'en'` | Interface language: `'en'` or `'ms-MY'` |

## API Methods

The widget exposes several methods for programmatic control:

```javascript
// Initialize the widget
HalalChatWidget.init(config);

// Destroy the widget
HalalChatWidget.destroy();

// Open the chat (if closed)
HalalChatWidget.open();

// Close the chat (if open)
HalalChatWidget.close();

// Check if chat is open
const isOpen = HalalChatWidget.isOpen();
```

## Development

### Prerequisites

- Node.js 16+
- pnpm

### Setup

```bash
# Install dependencies
pnpm install

# Start development server (opens at http://localhost:9000)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

### Project Structure

```
embeddable-chat/
├── src/
│   ├── ChatWidget.tsx      # Main widget component
│   ├── api.ts              # API integration
│   ├── types.ts            # TypeScript types
│   ├── styles.ts           # Inline styles
│   ├── icons.tsx           # SVG icons
│   ├── translations.ts     # Internationalization
│   ├── MarkdownRenderer.tsx # Markdown parsing
│   ├── SourcesDisplay.tsx  # Sources component
│   ├── useVoiceRecording.ts # Voice recording hook
│   ├── useImageUpload.ts   # Image upload hook
│   └── index.tsx           # Entry point
├── index.html              # Development example page
├── dist/                   # Built files
│   ├── halal-chat-widget.umd.js  # UMD build for browsers
│   └── halal-chat-widget.mjs     # ES module build
├── package.json
├── vite.config.ts          # Vite configuration
├── tsconfig.json
└── README.md
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## API Compatibility

This widget is compatible with the Halal Portal API endpoints:

- `POST /api/chat/session` - Create chat session
- `POST /api/chat/message` - Send text message
- `POST /api/chat/image` - Send image with prompt
- `POST /api/upload` - Upload files (images/audio)
- `GET /api/sites/{siteId}/bots/slug/{botSlug}` - Get bot configuration

## Security Considerations

- The widget makes requests to your API server
- Ensure your API server has proper CORS configuration
- Consider implementing rate limiting on your API endpoints
- Validate all user inputs on the server side

## Troubleshooting

### Widget Not Loading

1. Check that the script URL is correct
2. Verify the `baseURL` points to your API server
3. Check browser console for errors
4. Ensure CORS is properly configured on your API server

### Voice Recording Not Working

1. Ensure the website is served over HTTPS (required for microphone access)
2. Check that the user has granted microphone permissions
3. Verify the browser supports MediaRecorder API

### Image Upload Failing

1. Check file size limits (default: 10MB)
2. Verify supported file types (JPEG, PNG, GIF, WebP)
3. Ensure the `/api/upload` endpoint is accessible

## License

MIT License - see LICENSE file for details.

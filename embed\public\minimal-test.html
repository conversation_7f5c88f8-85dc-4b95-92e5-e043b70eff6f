<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
            margin: 0;
            min-height: 100vh;
        }
        .content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        /* Force visibility for debugging */
        #halal-chat-widget-container {
            border: 2px solid red !important;
            background: rgba(255, 0, 0, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>Minimal Chat Widget Test</h1>
        <p>This page tests the basic widget loading and initialization.</p>
        
        <button class="test-button" onclick="checkWidget()">Check Widget Status</button>
        <button class="test-button" onclick="forceInit()">Force Initialize</button>
        <button class="test-button" onclick="inspectDOM()">Inspect DOM</button>
        
        <div id="debug-output" class="debug">
            Waiting for widget to load...
        </div>
    </div>

    <!-- Load the widget -->
    <script src="/dist/halal-chat-widget.umd.js"></script>
    
    <script>
        let debugOutput = document.getElementById('debug-output');
        
        function log(message) {
            console.log(message);
            debugOutput.innerHTML += message + '<br>';
        }
        
        function checkWidget() {
            log('=== Widget Status Check ===');
            log('HalalChatWidget type: ' + typeof window.HalalChatWidget);
            
            if (window.HalalChatWidget) {
                log('Available methods: ' + Object.keys(window.HalalChatWidget).join(', '));
            }
            
            const container = document.getElementById('halal-chat-widget-container');
            log('Widget container exists: ' + (container !== null));
            
            if (container) {
                log('Container innerHTML length: ' + container.innerHTML.length);
                log('Container style: ' + container.getAttribute('style'));
                log('Container children count: ' + container.children.length);
            }
        }
        
        function forceInit() {
            log('=== Force Initialize ===');
            try {
                if (!window.HalalChatWidget) {
                    log('ERROR: HalalChatWidget not available');
                    return;
                }
                
                log('Calling HalalChatWidget.init...');
                window.HalalChatWidget.init({
                    baseURL: 'http://localhost:16001',
                    position: 'bottom-right',
                    theme: 'light',
                    language: 'en'
                });
                log('Init call completed');
                
                // Check again after a short delay
                setTimeout(() => {
                    checkWidget();
                }, 100);
                
            } catch (error) {
                log('ERROR during init: ' + error.message);
                log('Error stack: ' + error.stack);
            }
        }
        
        function inspectDOM() {
            log('=== DOM Inspection ===');
            
            // Check all elements with high z-index
            const allElements = document.querySelectorAll('*');
            let highZIndexElements = [];
            
            allElements.forEach(el => {
                const zIndex = window.getComputedStyle(el).zIndex;
                if (zIndex && zIndex !== 'auto' && parseInt(zIndex) > 1000) {
                    highZIndexElements.push({
                        tag: el.tagName,
                        id: el.id,
                        className: el.className,
                        zIndex: zIndex
                    });
                }
            });
            
            log('High z-index elements: ' + JSON.stringify(highZIndexElements, null, 2));
            
            // Check for any elements with 'chat' or 'widget' in their attributes
            const chatElements = document.querySelectorAll('[id*="chat"], [class*="chat"], [id*="widget"], [class*="widget"]');
            log('Chat/widget related elements: ' + chatElements.length);
            
            chatElements.forEach((el, index) => {
                log(`Element ${index}: ${el.tagName} id="${el.id}" class="${el.className}"`);
            });
        }
        
        // Auto-check on load
        window.addEventListener('load', () => {
            log('Page loaded');
            checkWidget();
        });
        
        // Check periodically
        let checkCount = 0;
        const interval = setInterval(() => {
            checkCount++;
            if (checkCount > 10) {
                clearInterval(interval);
                return;
            }
            
            if (window.HalalChatWidget && !document.getElementById('halal-chat-widget-container')) {
                log(`Auto-check ${checkCount}: Widget available but not initialized`);
            }
        }, 1000);
    </script>
</body>
</html>

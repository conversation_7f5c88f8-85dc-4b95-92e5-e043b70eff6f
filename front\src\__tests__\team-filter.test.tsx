import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { TeamFilter } from '@/components/ui/team-filter'
import { useTeamsStore } from '@/stores/teams'
import type { Team } from '@/types/team'

// Mock the teams store
jest.mock('@/stores/teams', () => ({
  useTeamsStore: jest.fn(),
}))

const mockTeams: Team[] = [
  {
    id: 1,
    siteId: 1,
    name: 'Support Team',
    description: 'Customer support agents',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    siteId: 1,
    name: 'Sales Team',
    description: 'Sales representatives',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: 3,
    siteId: 1,
    name: 'Inactive Team',
    description: 'This team is inactive',
    isActive: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
]

describe('TeamFilter Component', () => {
  const mockOnFilterChange = jest.fn()
  const mockFetchTeams = jest.fn()
  const mockSetTeamFilter = jest.fn()
  const mockGetTeamName = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      teamFilter: {
        showAllTeams: true,
        selectedTeamId: null,
        availableTeams: mockTeams.filter(t => t.isActive),
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })
  })

  it('should render team filter with all teams option', () => {
    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(screen.getByText('Filter by Team:')).toBeInTheDocument()
    expect(screen.getByDisplayValue('')).toBeInTheDocument()
    expect(screen.getByText('All Teams')).toBeInTheDocument()
  })

  it('should display only active teams in dropdown', () => {
    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    const select = screen.getByRole('combobox')
    
    // Check that active teams are present
    expect(screen.getByText('Support Team')).toBeInTheDocument()
    expect(screen.getByText('Sales Team')).toBeInTheDocument()
    
    // Check that inactive team is not present
    expect(screen.queryByText('Inactive Team')).not.toBeInTheDocument()
  })

  it('should call onFilterChange when team is selected', () => {
    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    const select = screen.getByRole('combobox')
    fireEvent.change(select, { target: { value: '1' } })

    expect(mockSetTeamFilter).toHaveBeenCalledWith({
      selectedTeamId: 1,
      showAllTeams: false,
    })
    expect(mockOnFilterChange).toHaveBeenCalledWith(1, false)
  })

  it('should call onFilterChange with null when "All Teams" is selected', () => {
    // Set initial state with a team selected
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      teamFilter: {
        showAllTeams: false,
        selectedTeamId: 1,
        availableTeams: mockTeams.filter(t => t.isActive),
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    const select = screen.getByRole('combobox')
    fireEvent.change(select, { target: { value: '' } })

    expect(mockSetTeamFilter).toHaveBeenCalledWith({
      selectedTeamId: null,
      showAllTeams: true,
    })
    expect(mockOnFilterChange).toHaveBeenCalledWith(null, true)
  })

  it('should show clear filter button when team is selected', () => {
    mockGetTeamName.mockReturnValue('Support Team')
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      teamFilter: {
        showAllTeams: false,
        selectedTeamId: 1,
        availableTeams: mockTeams.filter(t => t.isActive),
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(screen.getByText('Support Team')).toBeInTheDocument()
    expect(screen.getByTitle('Clear team filter')).toBeInTheDocument()
  })

  it('should clear filter when clear button is clicked', () => {
    mockGetTeamName.mockReturnValue('Support Team')
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      teamFilter: {
        showAllTeams: false,
        selectedTeamId: 1,
        availableTeams: mockTeams.filter(t => t.isActive),
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    const clearButton = screen.getByTitle('Clear team filter')
    fireEvent.click(clearButton)

    expect(mockSetTeamFilter).toHaveBeenCalledWith({
      selectedTeamId: null,
      showAllTeams: true,
    })
    expect(mockOnFilterChange).toHaveBeenCalledWith(null, true)
  })

  it('should fetch teams on mount when teams array is empty', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      teamFilter: {
        showAllTeams: true,
        selectedTeamId: null,
        availableTeams: [],
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(mockFetchTeams).toHaveBeenCalled()
  })

  it('should show loading state', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      teamFilter: {
        showAllTeams: true,
        selectedTeamId: null,
        availableTeams: [],
      },
      isLoading: true,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(screen.getByText('Loading teams...')).toBeInTheDocument()
  })

  it('should show no teams message when no active teams available', () => {
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: [],
      teamFilter: {
        showAllTeams: true,
        selectedTeamId: null,
        availableTeams: [],
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(screen.getByText('No teams available')).toBeInTheDocument()
  })

  it('should show current filter status when team is selected', () => {
    mockGetTeamName.mockReturnValue('Support Team')
    ;(useTeamsStore as jest.Mock).mockReturnValue({
      teams: mockTeams,
      teamFilter: {
        showAllTeams: false,
        selectedTeamId: 1,
        availableTeams: mockTeams.filter(t => t.isActive),
      },
      isLoading: false,
      fetchTeams: mockFetchTeams,
      setTeamFilter: mockSetTeamFilter,
      getTeamName: mockGetTeamName,
    })

    render(<TeamFilter onFilterChange={mockOnFilterChange} />)

    expect(screen.getByText('Showing sessions for:')).toBeInTheDocument()
    expect(screen.getByText('Support Team')).toBeInTheDocument()
  })
})

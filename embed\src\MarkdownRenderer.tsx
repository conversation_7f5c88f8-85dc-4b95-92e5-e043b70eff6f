import React from 'react';

interface MarkdownRendererProps {
  content: string;
  style?: React.CSSProperties;
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, style }) => {
  const baseStyle: React.CSSProperties = {
    lineHeight: '1.5',
    color: 'inherit',
    ...style,
  };

  // Simple markdown parsing - handles basic formatting
  const parseMarkdown = (text: string): React.ReactNode[] => {
    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];
    let currentParagraph: string[] = [];
    let listItems: string[] = [];
    let inCodeBlock = false;
    let codeBlockContent: string[] = [];

    const flushParagraph = () => {
      if (currentParagraph.length > 0) {
        elements.push(
          <p key={elements.length} style={{ margin: '0 0 8px 0' }}>
            {parseInlineMarkdown(currentParagraph.join('\n'))}
          </p>
        );
        currentParagraph = [];
      }
    };

    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <ul key={elements.length} style={{ margin: '0 0 8px 0', paddingLeft: '20px' }}>
            {listItems.map((item, index) => (
              <li key={index} style={{ marginBottom: '4px' }}>
                {parseInlineMarkdown(item)}
              </li>
            ))}
          </ul>
        );
        listItems = [];
      }
    };

    const flushCodeBlock = () => {
      if (codeBlockContent.length > 0) {
        elements.push(
          <pre key={elements.length} style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '4px',
            padding: '8px',
            margin: '0 0 8px 0',
            fontSize: '12px',
            fontFamily: 'monospace',
            overflow: 'auto',
            whiteSpace: 'pre-wrap',
          }}>
            <code>{codeBlockContent.join('\n')}</code>
          </pre>
        );
        codeBlockContent = [];
      }
    };

    lines.forEach((line, index) => {
      // Handle code blocks
      if (line.trim().startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
          inCodeBlock = false;
        } else {
          flushParagraph();
          flushList();
          inCodeBlock = true;
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // Handle headers
      if (line.startsWith('#')) {
        flushParagraph();
        flushList();
        const level = line.match(/^#+/)?.[0].length || 1;
        const text = line.replace(/^#+\s*/, '');
        const fontSize = Math.max(16 - level, 12);
        elements.push(
          <h3 key={elements.length} style={{
            fontSize: `${fontSize}px`,
            fontWeight: '600',
            margin: '12px 0 6px 0',
            color: 'inherit',
          }}>
            {parseInlineMarkdown(text)}
          </h3>
        );
        return;
      }

      // Handle list items
      if (line.match(/^\s*[-*+]\s/)) {
        flushParagraph();
        const item = line.replace(/^\s*[-*+]\s/, '');
        listItems.push(item);
        return;
      }

      // Handle numbered lists
      if (line.match(/^\s*\d+\.\s/)) {
        flushParagraph();
        flushList(); // Flush unordered list first
        const item = line.replace(/^\s*\d+\.\s/, '');
        // For simplicity, treat numbered lists as unordered
        listItems.push(item);
        return;
      }

      // Handle empty lines
      if (line.trim() === '') {
        flushParagraph();
        flushList();
        return;
      }

      // Regular paragraph content
      flushList();
      currentParagraph.push(line);
    });

    // Flush remaining content
    flushParagraph();
    flushList();
    flushCodeBlock();

    return elements;
  };

  // Parse inline markdown (bold, italic, code, links)
  const parseInlineMarkdown = (text: string): React.ReactNode[] => {
    const elements: React.ReactNode[] = [];
    let currentText = text;
    let key = 0;

    // Process inline elements
    while (currentText.length > 0) {
      // Bold text (**text** or __text__)
      const boldMatch = currentText.match(/^(.*?)(\*\*|__)(.*?)\2/);
      if (boldMatch) {
        if (boldMatch[1]) {
          elements.push(boldMatch[1]);
        }
        elements.push(
          <strong key={key++} style={{ fontWeight: '600' }}>
            {boldMatch[3]}
          </strong>
        );
        currentText = currentText.slice(boldMatch[0].length);
        continue;
      }

      // Italic text (*text* or _text_)
      const italicMatch = currentText.match(/^(.*?)(\*|_)(.*?)\2/);
      if (italicMatch) {
        if (italicMatch[1]) {
          elements.push(italicMatch[1]);
        }
        elements.push(
          <em key={key++} style={{ fontStyle: 'italic' }}>
            {italicMatch[3]}
          </em>
        );
        currentText = currentText.slice(italicMatch[0].length);
        continue;
      }

      // Inline code (`code`)
      const codeMatch = currentText.match(/^(.*?)`(.*?)`/);
      if (codeMatch) {
        if (codeMatch[1]) {
          elements.push(codeMatch[1]);
        }
        elements.push(
          <code key={key++} style={{
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '2px',
            padding: '1px 4px',
            fontSize: '11px',
            fontFamily: 'monospace',
          }}>
            {codeMatch[2]}
          </code>
        );
        currentText = currentText.slice(codeMatch[0].length);
        continue;
      }

      // Links [text](url)
      const linkMatch = currentText.match(/^(.*?)\[([^\]]+)\]\(([^)]+)\)/);
      if (linkMatch) {
        if (linkMatch[1]) {
          elements.push(linkMatch[1]);
        }
        elements.push(
          <a key={key++} href={linkMatch[3]} target="_blank" rel="noopener noreferrer" style={{
            color: '#007bff',
            textDecoration: 'none',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.textDecoration = 'underline';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.textDecoration = 'none';
          }}>
            {linkMatch[2]}
          </a>
        );
        currentText = currentText.slice(linkMatch[0].length);
        continue;
      }

      // No more special formatting, add the rest as plain text
      elements.push(currentText);
      break;
    }

    return elements;
  };

  return (
    <div style={baseStyle}>
      {parseMarkdown(content)}
    </div>
  );
};

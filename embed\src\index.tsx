import React from 'react';
import { createRoot } from 'react-dom/client';
import { ChatWidget } from './ChatWidget';
import { ChatWidgetConfig } from './types';

// Global interface for the widget
interface HalalChatWidgetGlobal {
  init: (config: ChatWidgetConfig) => void;
  destroy: () => void;
  open: () => void;
  close: () => void;
  isOpen: () => boolean;
}

class HalalChatWidgetManager {
  private root: any = null;
  private container: HTMLElement | null = null;
  private config: ChatWidgetConfig | null = null;
  private isWidgetOpen = false;

  init(config: ChatWidgetConfig) {
    if (!config.baseURL) {
      throw new Error('baseURL is required');
    }

    this.config = {
      position: 'bottom-right',
      theme: 'light',
      language: 'en',
      ...config,
    };

    this.destroy(); // Clean up any existing widget
    this.createWidget();
  }

  private createWidget() {
    if (!this.config) return;

    // Create container
    this.container = document.createElement('div');
    this.container.id = 'halal-chat-widget-container';
    this.container.style.cssText = `
      position: fixed;
      z-index: 999999;
      pointer-events: none;
    `;
    
    // Make only the widget interactive
    this.container.addEventListener('click', (e) => {
      e.stopPropagation();
    });

    document.body.appendChild(this.container);

    // Create React root and render
    this.root = createRoot(this.container);
    this.renderWidget();
  }

  private renderWidget() {
    if (!this.root || !this.config) return;

    const WidgetWrapper = () => {
      return (
        <div style={{ pointerEvents: 'auto' }}>
          <ChatWidget config={this.config!} />
        </div>
      );
    };

    this.root.render(<WidgetWrapper />);
  }

  destroy() {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container) {
      document.body.removeChild(this.container);
      this.container = null;
    }
    this.config = null;
    this.isWidgetOpen = false;
  }

  open() {
    // This would need to be implemented by passing a ref or state to ChatWidget
    console.log('Open method not yet implemented');
  }

  close() {
    // This would need to be implemented by passing a ref or state to ChatWidget
    console.log('Close method not yet implemented');
  }

  isOpen() {
    return this.isWidgetOpen;
  }
}

// Create global instance
const widgetManager = new HalalChatWidgetManager();

// Global API
const HalalChatWidget: HalalChatWidgetGlobal = {
  init: (config: ChatWidgetConfig) => widgetManager.init(config),
  destroy: () => widgetManager.destroy(),
  open: () => widgetManager.open(),
  close: () => widgetManager.close(),
  isOpen: () => widgetManager.isOpen(),
};

// Make it available globally
if (typeof window !== 'undefined') {
  (window as any).HalalChatWidget = HalalChatWidget;
}

// Auto-initialize if config is provided via data attributes
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    const script = document.querySelector('script[src*="halal-chat-widget"]');
    if (script) {
      const baseURL = script.getAttribute('data-base-url');
      const botSlug = script.getAttribute('data-bot-slug');
      const position = script.getAttribute('data-position') as 'bottom-right' | 'bottom-left';
      const theme = script.getAttribute('data-theme') as 'light' | 'dark';
      const language = script.getAttribute('data-language') as 'en' | 'ms-MY';

      if (baseURL) {
        HalalChatWidget.init({
          baseURL,
          ...(botSlug && { botSlug }),
          ...(position && { position }),
          ...(theme && { theme }),
          ...(language && { language }),
        });
      }
    }
  });
}

export default HalalChatWidget;

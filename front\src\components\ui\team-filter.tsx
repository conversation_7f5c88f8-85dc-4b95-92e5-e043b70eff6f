'use client'

import { Filter, Users, X } from 'lucide-react'
import { useEffect } from 'react'
import { useTeamsStore } from '@/stores/teams'

interface TeamFilterProps {
  onFilterChange: (teamId: number | null, showAllTeams: boolean) => void
  className?: string
}

export function TeamFilter({ onFilterChange, className = '' }: TeamFilterProps) {
  const { 
    teams, 
    teamFilter, 
    isLoading, 
    fetchTeams, 
    setTeamFilter, 
    getTeamName 
  } = useTeamsStore()

  useEffect(() => {
    // Fetch teams when component mounts
    if (teams.length === 0) {
      fetchTeams()
    }
  }, [teams.length, fetchTeams])

  const handleTeamSelect = (teamId: number | null) => {
    const showAllTeams = teamId === null
    setTeamFilter({
      selectedTeamId: teamId,
      showAllTeams,
    })
    onFilterChange(teamId, showAllTeams)
  }

  const clearFilter = () => {
    handleTeamSelect(null)
  }

  const activeTeams = teams.filter(team => team.isActive)

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className="flex items-center space-x-2">
        <Filter className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">Filter by Team:</span>
      </div>

      <div className="flex items-center space-x-2">
        <select
          value={teamFilter.selectedTeamId?.toString() || ''}
          onChange={(e) => {
            const value = e.target.value
            handleTeamSelect(value ? parseInt(value, 10) : null)
          }}
          disabled={isLoading}
          className="block w-48 px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
        >
          <option value="">All Teams</option>
          {activeTeams.map(team => (
            <option key={team.id} value={team.id.toString()}>
              {team.name}
            </option>
          ))}
        </select>

        {!teamFilter.showAllTeams && teamFilter.selectedTeamId && (
          <button
            onClick={clearFilter}
            className="flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
            title="Clear team filter"
          >
            <span className="mr-1">
              {getTeamName(teamFilter.selectedTeamId)}
            </span>
            <X className="h-3 w-3" />
          </button>
        )}
      </div>

      {isLoading && (
        <div className="flex items-center space-x-1 text-sm text-gray-500">
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400"></div>
          <span>Loading teams...</span>
        </div>
      )}

      {!isLoading && activeTeams.length === 0 && (
        <div className="flex items-center space-x-1 text-sm text-gray-500">
          <Users className="h-3 w-3" />
          <span>No teams available</span>
        </div>
      )}

      {!teamFilter.showAllTeams && (
        <div className="text-xs text-gray-500">
          Showing sessions for: <span className="font-medium">{getTeamName(teamFilter.selectedTeamId)}</span>
        </div>
      )}
    </div>
  )
}

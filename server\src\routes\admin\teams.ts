import { Hono } from 'hono'
import {
  createTeam,
  deleteTeam,
  getTeam,
  getTeams,
  updateTeam,
} from '@/controllers/adminTeamsController'
import { authenticateAdmin } from '@/middleware/auth'

const teamsRouter = new Hono()

// Apply authentication middleware to all routes
teamsRouter.use('*', authenticateAdmin)

// CRUD operations
teamsRouter.post('/', createTeam)
teamsRouter.get('/', getTeams)
teamsRouter.get('/:id', getTeam)
teamsRouter.put('/:id', updateTeam)
teamsRouter.delete('/:id', deleteTeam)

export default teamsRouter
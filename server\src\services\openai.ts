import dotenv from 'dotenv'
import OpenAI from 'openai'
import type {
  ChatMessage,
  MessageWithToolCalls,
  OpenAIResponse,
  TextToSpeechResponse,
  ToolCall,
  ToolDefinition,
  ToolMessage,
  TranscriptionResponse,
} from '../types'
import { AI_CONFIG } from '../constants'

// Load environment variables for non-Cloudflare environments
if (typeof process !== 'undefined' && process.env) {
  dotenv.config()
}

class OpenAIService {
  private client: OpenAI | null = null

  private getClient(env?: any): OpenAI {
    if (this.client) {
      return this.client
    }
    // console.log('Initializing OpenAI client...', { env, process: process.env });
    // Try to get API key from environment (Cloudflare Workers) or process.env (Node.js)
    const apiKey = env?.OPENAI_API_KEY || process.env?.OPENAI_API_KEY

    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is required')
    }

    this.client = new OpenAI({
      apiKey: apiKey,
    })

    return this.client
  }

  async sendTextMessage(
    messages: Omit<ChatMessage, 'timestamp'>[],
    model = 'gpt-4.1-mini',
    env?: any,
    temperature = 0.7,
  ): Promise<OpenAIResponse> {
    try {
      const client = this.getClient(env)

      // Convert messages to OpenAI format, mapping 'agent' role to 'assistant'
      const openaiMessages = messages.map((msg) => ({
        role: msg.role === 'agent' ? 'assistant' : msg.role,
        content: msg.content,
      })) as Array<{ role: 'system' | 'user' | 'assistant'; content: string }>

      const response = await client.chat.completions.create({
        model,
        messages: openaiMessages,
        max_tokens: 1000,
        temperature,
      })

      return {
        success: true,
        message: response.choices[0]?.message?.content || '',
        usage: response.usage || undefined,
      }
    } catch (error) {
      console.error('OpenAI text chat error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  async sendMessageWithTools(
    messages: (
      | Omit<ChatMessage, 'timestamp'>
      | MessageWithToolCalls
      | ToolMessage
    )[],
    tools: ToolDefinition[],
    model = AI_CONFIG.DEFAULT_MODEL,
    env?: any,
    temperature = 0.7,
  ): Promise<OpenAIResponse> {
    try {
      const client = this.getClient(env)

      // Convert messages to OpenAI format
      const openaiMessages = messages.map((msg) => {
        if ('toolCallId' in msg && msg.role === 'tool') {
          // Tool message
          return {
            role: 'tool' as const,
            content: msg.content,
            tool_call_id: msg.toolCallId,
          }
        }

        if ('toolCalls' in msg && msg.toolCalls) {
          // Assistant message with tool calls
          return {
            role: 'assistant' as const,
            content: msg.content,
            tool_calls: msg.toolCalls.map((tc) => ({
              id: tc.id,
              type: tc.type,
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments,
              },
            })),
          }
        }

        // Regular message
        return {
          role:
            msg.role === 'agent'
              ? ('assistant' as const)
              : msg.role === 'tool'
                ? ('assistant' as const)
                : // Convert tool to assistant for regular messages
                  (msg.role as 'system' | 'user' | 'assistant'),
          content: msg.content,
        }
      })

      const response = await client.chat.completions.create({
        model,
        messages: openaiMessages,
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        max_tokens: 1000,
        temperature,
      })

      const choice = response.choices[0]
      const toolCalls = choice?.message?.tool_calls?.map((tc) => ({
        id: tc.id,
        type: tc.type,
        function: {
          name: tc.function.name,
          arguments: tc.function.arguments,
        },
      })) as ToolCall[] | undefined

      return {
        success: true,
        message: choice?.message?.content || '',
        usage: response.usage || undefined,
        toolCalls,
        finishReason: choice?.finish_reason as
          | 'stop'
          | 'length'
          | 'tool_calls'
          | 'content_filter'
          | undefined,
      }
    } catch (error) {
      console.error('OpenAI tool calling error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  async analyzeImage(
    imageUrl: string,
    prompt = "What's in this image?",
    model = 'gpt-4.1-mini',
    env?: any,
  ): Promise<OpenAIResponse> {
    try {
      const client = this.getClient(env)
      const response = await client.chat.completions.create({
        model,
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              {
                type: 'image_url',
                image_url: {
                  url: imageUrl,
                },
              },
            ],
          },
        ],
        max_tokens: 1000,
      })

      return {
        success: true,
        message: response.choices[0]?.message?.content || '',
        usage: response.usage || undefined,
      }
    } catch (error) {
      console.error('OpenAI image analysis error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  /**
   * Analyze document using OpenAI Vision API
   *
   * IMPORTANT: OpenAI Vision API only supports image formats (PNG, JPEG, GIF, WebP).
   * Document formats (PDF, DOC, TXT, etc.) will fail with "unsupported image" error.
   *
   * This method uses the same Vision API as images because:
   * - OpenAI has no separate document API
   * - Must use 'image_url' field even for documents (OpenAI requirement)
   * - Future OpenAI updates might add document support to Vision API
   *
   * @param documentUrl - S3 URL of the document
   * @param prompt - Analysis prompt
   * @param model - OpenAI model to use
   * @param env - Environment variables
   * @returns OpenAI response (will fail for non-image formats)
   */
  async analyzeDocument(
    documentUrl: string,
    prompt = 'Please analyze this document and provide a summary of its contents.',
    model = 'gpt-4.1-mini',
    env?: any,
  ): Promise<OpenAIResponse> {
    try {
      const client = this.getClient(env)
      const response = await client.chat.completions.create({
        model,
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              {
                // NOTE: Must use 'image_url' even for documents - OpenAI API requirement
                type: 'image_url',
                image_url: {
                  url: documentUrl,
                },
              },
            ],
          },
        ],
        max_tokens: 1000,
      })

      return {
        success: true,
        message: response.choices[0]?.message?.content || '',
        usage: response.usage || undefined,
      }
    } catch (error) {
      console.error('OpenAI document analysis error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  async transcribeAudio(
    audioFile: File,
    env?: any,
  ): Promise<TranscriptionResponse> {
    try {
      const client = this.getClient(env)
      const response = await client.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
      })

      return {
        success: true,
        text: response.text,
      }
    } catch (error) {
      console.error('OpenAI transcription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  async textToSpeech(
    text: string,
    voice: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer' = 'alloy',
    model = 'tts-1',
    env?: any,
  ): Promise<TextToSpeechResponse> {
    try {
      const client = this.getClient(env)
      const response = await client.audio.speech.create({
        model,
        voice,
        input: text,
      })

      return {
        success: true,
        audioBuffer: Buffer.from(await response.arrayBuffer()),
      }
    } catch (error) {
      console.error('OpenAI text-to-speech error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  // Tool definitions
  getAvailableTools(includeFormTools: boolean = false): ToolDefinition[] {
    const tools = [
      {
        type: 'function',
        function: {
          name: 'search_halal_knowledge',
          description:
            'Search for information about halal topics, Islamic jurisprudence, halal food, Islamic practices, and religious matters. Use this tool ONCE when users ask questions related to Islam, halal/haram rulings, Islamic law, or religious guidance. The tool returns a complete answer that you should use to respond to the user directly.',
          parameters: {
            type: 'object',
            properties: {
              query: {
                type: 'string',
                description: 'The search query about halal or Islamic topics',
              },
              maxResults: {
                type: 'number',
                description:
                  'Maximum number of search results to return (default: 5)',
              },
              minScore: {
                type: 'number',
                description:
                  'Minimum relevance score for results (default: 0.3)',
              },
            },
            required: ['query'],
          },
        },
      },
    ];

    // Add form tools if enabled
    if (includeFormTools) {
      tools.push(...this.getFormTools());
    }

    return tools;
  }

  // Form manipulation tools for browser-side execution
  getFormTools(): ToolDefinition[] {
    return [
      {
        type: 'function',
        function: {
          name: 'query_elements',
          description: 'Query DOM elements using CSS selectors, IDs, names, classes, or XPath. Use this to find elements on the page.',
          parameters: {
            type: 'object',
            properties: {
              selector: {
                type: 'string',
                description: 'CSS selector, ID (#id), class (.class), name ([name="value"]), or XPath to find elements'
              },
              queryType: {
                type: 'string',
                enum: ['css', 'id', 'name', 'class', 'tag', 'xpath'],
                description: 'Type of query to perform (default: css)'
              }
            },
            required: ['selector']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'fill_field',
          description: 'Fill a form field with a specified value. Works with text inputs, textareas, selects, checkboxes, and radio buttons.',
          parameters: {
            type: 'object',
            properties: {
              selector: {
                type: 'string',
                description: 'CSS selector to identify the form field (e.g., "#email", "[name=\'firstName\']", ".input-class")'
              },
              value: {
                type: 'string',
                description: 'Value to fill in the field. For checkboxes/radio: "true"/"false" or "1"/"0"'
              }
            },
            required: ['selector', 'value']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'select_option',
          description: 'Select option(s) in a dropdown or multi-select element.',
          parameters: {
            type: 'object',
            properties: {
              selector: {
                type: 'string',
                description: 'CSS selector for the select element'
              },
              values: {
                type: 'array',
                items: { type: 'string' },
                description: 'Array of option values or text to select'
              }
            },
            required: ['selector', 'values']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'get_field_value',
          description: 'Get the current value of a form field.',
          parameters: {
            type: 'object',
            properties: {
              selector: {
                type: 'string',
                description: 'CSS selector for the form field'
              }
            },
            required: ['selector']
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'get_form_data',
          description: 'Get all data from a form as a key-value object.',
          parameters: {
            type: 'object',
            properties: {
              formSelector: {
                type: 'string',
                description: 'CSS selector for the form element (default: "form")'
              }
            },
            required: []
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'validate_form',
          description: 'Validate a form and get information about invalid fields.',
          parameters: {
            type: 'object',
            properties: {
              formSelector: {
                type: 'string',
                description: 'CSS selector for the form element (default: "form")'
              }
            },
            required: []
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'get_empty_required_fields',
          description: 'Get a list of required fields that are currently empty or invalid.',
          parameters: {
            type: 'object',
            properties: {
              formSelector: {
                type: 'string',
                description: 'CSS selector for the form element (default: "form")'
              }
            },
            required: []
          }
        }
      },
      {
        type: 'function',
        function: {
          name: 'get_form_summary',
          description: 'Get a comprehensive summary of the form including all fields, their current values, and validation status.',
          parameters: {
            type: 'object',
            properties: {
              formSelector: {
                type: 'string',
                description: 'CSS selector for the form element (default: "form")'
              }
            },
            required: []
          }
        }
      }
    ];
  }
}

export default new OpenAIService()

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChatWidgetConfig, Message, UploadedImage, ToolCall, FormToolExecutionRequest, FormToolExecutionResponse } from './types';
import { ChatAPI } from './api';
import { useVoiceRecording } from './useVoiceRecording';
import { useImageUpload, useDragAndDrop } from './useImageUpload';
import { createTranslationFunction, Language } from './translations';
import { styles } from './styles';
import {
  MessageCircleIcon,
  SendIcon,
  MicIcon,
  MicOffIcon,
  XIcon,
  MinimizeIcon,
  MaximizeIcon,
  UserIcon,
  ImageIcon,
} from './icons';
import { MarkdownRenderer } from './MarkdownRenderer';
import { SourcesDisplay } from './SourcesDisplay';
import { executeFormTool } from './form-tools';

interface ChatWidgetProps {
  config: ChatWidgetConfig;
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({ config }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [pendingToolCalls, setPendingToolCalls] = useState<ToolCall[]>([]);

  // Refs
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // API and hooks
  const api = new ChatAPI(config.baseURL, config.botSlug);
  const t = createTranslationFunction(config.language || 'en');

  // Voice recording
  const voiceRecording = useVoiceRecording({
    api,
    onTranscription: (text) => {
      setInputText(text);
      inputRef.current?.focus();
    },
  });

  // Image upload
  const imageUpload = useImageUpload({
    api,
    onImageUploaded: (image) => {
      console.log('Image uploaded:', image);
    },
  });

  // Drag and drop
  const dragAndDrop = useDragAndDrop({
    onDrop: async (files) => {
      const imageFiles = files.filter(file => file.type.startsWith('image/'));
      if (imageFiles.length > 0) {
        await imageUpload.uploadImage(imageFiles[0]);
      }
    },
    accept: ['image/*'],
  });

  // Initialize session
  const initializeSession = useCallback(async () => {
    try {
      setConnectionError(null);
      const response = await api.createSession();
      
      if (response.success && response.data?.sessionId) {
        setSessionId(response.data.sessionId);
        
        // Add welcome message
        const welcomeMessage: Message = {
          id: 'welcome',
          role: 'assistant',
          content: t('welcome'),
          timestamp: new Date(),
        };
        setMessages([welcomeMessage]);
      } else {
        setConnectionError(response.error || t('sessionError'));
      }
    } catch (error) {
      console.error('Failed to initialize session:', error);
      setConnectionError(t('connectionError'));
    }
  }, [api, t]);

  // Send message
  const sendMessage = useCallback(async (text?: string, imageUrl?: string) => {
    if (!sessionId || (!text && !imageUrl)) return;

    const messageText = text || inputText.trim();
    if (!messageText && !imageUrl) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      imageUrl,
    };
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    imageUpload.clearImage();
    setIsLoading(true);

    try {
      let response;
      if (imageUrl) {
        response = await api.sendImage(sessionId, imageUrl, messageText);
      } else {
        response = await api.sendMessage(sessionId, messageText);
      }

      if (response.success) {
        // Check if there are tool calls to execute
        if (response.toolCalls && response.toolCalls.length > 0 && config.enableFormTools) {
          // Execute form tools
          await executeFormTools(response.toolCalls);
        } else {
          // Regular message response
          const assistantMessage: Message = {
            id: (Date.now() + 1).toString(),
            role: 'assistant',
            content: response.message || response.answer || 'No response received',
            timestamp: new Date(),
            sources: response.sources,
          };
          setMessages(prev => [...prev, assistantMessage]);

          // Show notification if chat is closed
          if (!isOpen) {
            setHasNewMessage(true);
          }
        }
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        role: 'assistant',
        content: t('connectionError'),
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [sessionId, inputText, imageUpload, api, t, isOpen, executeFormTools, config.enableFormTools]);

  // Execute form tools when requested by the AI
  const executeFormTools = useCallback(async (toolCalls: ToolCall[]) => {
    if (!config.enableFormTools || !sessionId) return;

    const toolResults: FormToolExecutionResponse[] = [];

    for (const toolCall of toolCalls) {
      try {
        // Parse the tool arguments
        const args = JSON.parse(toolCall.function.arguments);

        // Execute the form tool
        const result = await executeFormTool(toolCall.function.name, args);

        toolResults.push({
          toolCallId: toolCall.id,
          success: result.success,
          result: result.result,
          error: result.error
        });

        // Add a system message showing the tool execution
        const toolMessage: Message = {
          id: `tool-${toolCall.id}`,
          role: 'assistant',
          content: `🔧 **${toolCall.function.name}**: ${result.success ? result.result?.message || 'Executed successfully' : result.error || 'Failed to execute'}`,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, toolMessage]);

      } catch (error) {
        console.error('Form tool execution error:', error);
        toolResults.push({
          toolCallId: toolCall.id,
          success: false,
          result: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Send tool results back to the server
    try {
      const response = await api.sendToolResults(sessionId, toolResults);

      if (response.success && response.message) {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.message,
          timestamp: new Date(),
          sources: response.sources,
        };
        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      console.error('Failed to send tool results:', error);
    }
  }, [config.enableFormTools, sessionId, api]);

  // Auto-scroll to bottom
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Add CSS animations
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
      @keyframes bounce {
        0%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-6px); }
      }
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Initialize session when widget opens
  useEffect(() => {
    if (isOpen && !sessionId) {
      initializeSession();
    }
  }, [isOpen, sessionId, initializeSession]);

  // Clear notification when opening
  useEffect(() => {
    if (isOpen) {
      setHasNewMessage(false);
      inputRef.current?.focus();
    } else {
      setIsMaximized(false);
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputText.trim() || imageUpload.uploadedImage) {
      sendMessage(inputText, imageUpload.uploadedImage?.url);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Position styles
  const positionStyle = config.position === 'bottom-left' ? styles.bottomLeft : styles.bottomRight;

  if (!isOpen) {
    return (
      <div style={{ ...styles.widgetContainer, ...positionStyle }}>
        <button
          onClick={() => setIsOpen(true)}
          style={styles.chatButton}
          onMouseOver={(e) => {
            Object.assign(e.currentTarget.style, styles.chatButtonHover);
          }}
          onMouseOut={(e) => {
            Object.assign(e.currentTarget.style, styles.chatButton);
          }}
          title={t('chatTitle')}
        >
          <MessageCircleIcon size={24} color="white" />
          {hasNewMessage && <div style={styles.notificationDot} />}
        </button>
      </div>
    );
  }

  return (
    <div style={{ ...styles.widgetContainer, ...positionStyle }}>
      <div 
        style={{
          ...styles.chatWindow,
          ...(isMaximized ? styles.chatWindowMaximized : {}),
          position: 'relative',
        }}
        {...dragAndDrop.getRootProps()}
      >
        <input {...dragAndDrop.getInputProps()} ref={fileInputRef} />
        
        {/* Drag overlay */}
        {dragAndDrop.isDragActive && (
          <div style={styles.dragOverlay}>
            <div>
              <ImageIcon size={32} />
              <div style={{ marginTop: '8px' }}>{t('dragDropText')}</div>
            </div>
          </div>
        )}

        {/* Header */}
        <div style={styles.chatHeader}>
          <div>
            <div style={styles.headerTitle}>{t('chatTitle')}</div>
            <div style={styles.headerSubtitle}>{t('chatSubtitle')}</div>
          </div>
          <div style={styles.headerButtons}>
            <button
              onClick={() => setIsMaximized(!isMaximized)}
              style={styles.headerButton}
              onMouseOver={(e) => {
                Object.assign(e.currentTarget.style, styles.headerButtonHover);
              }}
              onMouseOut={(e) => {
                Object.assign(e.currentTarget.style, styles.headerButton);
              }}
              title={isMaximized ? t('minimize') : t('maximize')}
            >
              {isMaximized ? <MinimizeIcon size={16} /> : <MaximizeIcon size={16} />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              style={styles.headerButton}
              onMouseOver={(e) => {
                Object.assign(e.currentTarget.style, styles.headerButtonHover);
              }}
              onMouseOut={(e) => {
                Object.assign(e.currentTarget.style, styles.headerButton);
              }}
              title={t('close')}
            >
              <XIcon size={16} />
            </button>
          </div>
        </div>

        {/* Messages */}
        <div ref={messagesContainerRef} style={styles.messagesContainer}>
          {connectionError && (
            <div style={styles.errorMessage}>
              {connectionError}
              <button
                onClick={() => {
                  setConnectionError(null);
                  initializeSession();
                }}
                style={styles.errorCloseButton}
              >
                Retry
              </button>
            </div>
          )}
          
          {messages.map((message) => (
            <div key={message.id} style={styles.messageWrapper}>
              <div
                style={{
                  ...styles.messageWrapper,
                  ...(message.role === 'user' ? styles.messageWrapperUser : styles.messageWrapperAssistant),
                }}
              >
                <div
                  style={{
                    ...styles.messageBubble,
                    ...(message.role === 'user' 
                      ? styles.messageBubbleUser 
                      : message.role === 'agent'
                      ? styles.messageBubbleAgent
                      : styles.messageBubbleAssistant),
                  }}
                >
                  {message.role === 'agent' && message.agentName && (
                    <div style={styles.agentInfo}>
                      <UserIcon size={12} style={styles.agentIcon} />
                      {message.agentName}
                    </div>
                  )}
                  
                  {message.imageUrl && (
                    <img
                      src={message.imageUrl}
                      alt="Uploaded"
                      style={styles.messageImage}
                    />
                  )}
                  
                  <MarkdownRenderer content={message.content} />
                  
                  {message.sources && message.sources.length > 0 && (
                    <SourcesDisplay sources={message.sources} />
                  )}
                  
                  <div style={styles.messageTimestamp}>
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div style={styles.messageWrapper}>
              <div style={styles.messageWrapperAssistant}>
                <div style={{ ...styles.messageBubble, ...styles.messageBubbleAssistant }}>
                  <div style={styles.typingIndicator}>
                    <div style={styles.typingDots}>
                      <div style={{ ...styles.typingDot, animationDelay: '0ms' }} />
                      <div style={{ ...styles.typingDot, animationDelay: '150ms' }} />
                      <div style={{ ...styles.typingDot, animationDelay: '300ms' }} />
                    </div>
                    <span>{t('typing')}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Input area */}
        <div style={styles.inputArea}>
          {/* Image preview */}
          {imageUpload.uploadedImage && (
            <div style={styles.imagePreview}>
              <img
                src={imageUpload.uploadedImage.url}
                alt="Preview"
                style={styles.imagePreviewImg}
              />
              <button
                onClick={imageUpload.clearImage}
                style={styles.imagePreviewClose}
                title="Remove image"
              >
                <XIcon size={10} />
              </button>
            </div>
          )}

          {/* Voice error */}
          {voiceRecording.error && (
            <div style={styles.errorMessage}>
              {voiceRecording.error}
              <button
                onClick={voiceRecording.clearError}
                style={styles.errorCloseButton}
              >
                <XIcon size={12} />
              </button>
            </div>
          )}

          {/* Upload error */}
          {imageUpload.error && (
            <div style={styles.errorMessage}>
              {imageUpload.error}
              <button
                onClick={imageUpload.clearError}
                style={styles.errorCloseButton}
              >
                <XIcon size={12} />
              </button>
            </div>
          )}

          <form onSubmit={handleSubmit} style={styles.inputForm}>
            <textarea
              ref={inputRef}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={t('placeholder')}
              style={styles.textInput}
              disabled={isLoading}
              rows={1}
            />

            {voiceRecording.isSupported && (
              <button
                type="button"
                onClick={voiceRecording.toggleRecording}
                style={{
                  ...styles.button,
                  ...(voiceRecording.isRecording ? styles.voiceButtonActive : styles.voiceButton),
                }}
                onMouseOver={(e) => {
                  if (!voiceRecording.isRecording) {
                    Object.assign(e.currentTarget.style, styles.voiceButtonHover);
                  }
                }}
                onMouseOut={(e) => {
                  Object.assign(e.currentTarget.style, {
                    ...styles.button,
                    ...(voiceRecording.isRecording ? styles.voiceButtonActive : styles.voiceButton),
                  });
                }}
                disabled={isLoading || voiceRecording.isProcessing}
                title={voiceRecording.isRecording ? t('stopRecording') : t('startRecording')}
              >
                {voiceRecording.isRecording ? (
                  <MicOffIcon size={16} />
                ) : (
                  <MicIcon size={16} />
                )}
              </button>
            )}

            <button
              type="submit"
              disabled={isLoading || (!inputText.trim() && !imageUpload.uploadedImage)}
              style={{
                ...styles.button,
                ...styles.sendButton,
                ...(isLoading || (!inputText.trim() && !imageUpload.uploadedImage) 
                  ? styles.sendButtonDisabled 
                  : {}),
              }}
              onMouseOver={(e) => {
                if (!isLoading && (inputText.trim() || imageUpload.uploadedImage)) {
                  Object.assign(e.currentTarget.style, styles.sendButtonHover);
                }
              }}
              onMouseOut={(e) => {
                Object.assign(e.currentTarget.style, {
                  ...styles.button,
                  ...styles.sendButton,
                  ...(isLoading || (!inputText.trim() && !imageUpload.uploadedImage) 
                    ? styles.sendButtonDisabled 
                    : {}),
                });
              }}
              title={t('send')}
            >
              <SendIcon size={16} />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

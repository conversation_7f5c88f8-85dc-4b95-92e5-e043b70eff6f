<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programmatic Control Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.open {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.closed {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Programmatic Control Example</h1>
        <p>This example demonstrates how to control the chat widget programmatically using JavaScript.</p>

        <div class="controls">
            <h3>Widget Controls</h3>
            <div id="status" class="status closed">Widget Status: Closed</div>
            
            <button id="initBtn" class="btn">Initialize Widget</button>
            <button id="openBtn" class="btn" disabled>Open Chat</button>
            <button id="closeBtn" class="btn" disabled>Close Chat</button>
            <button id="destroyBtn" class="btn danger" disabled>Destroy Widget</button>
        </div>

        <h3>Code Examples</h3>
        
        <h4>Initialize the Widget</h4>
        <div class="code-example">
HalalChatWidget.init({
    baseURL: 'http://localhost:16001',
    botSlug: 'halal-assistant',
    position: 'bottom-right',
    theme: 'light',
    language: 'en'
});
        </div>

        <h4>Control Methods</h4>
        <div class="code-example">
// Open the chat
HalalChatWidget.open();

// Close the chat
HalalChatWidget.close();

// Check if chat is open
const isOpen = HalalChatWidget.isOpen();

// Destroy the widget
HalalChatWidget.destroy();
        </div>

        <h3>Features Demonstrated</h3>
        <ul>
            <li>Initialize widget with custom configuration</li>
            <li>Programmatically open and close the chat</li>
            <li>Check widget status</li>
            <li>Destroy and recreate the widget</li>
            <li>Real-time status updates</li>
        </ul>
    </div>

    <!-- Halal Chat Widget -->
    <script src="../dist/halal-chat-widget.js"></script>
    <script>
        let isInitialized = false;

        // DOM elements
        const statusEl = document.getElementById('status');
        const initBtn = document.getElementById('initBtn');
        const openBtn = document.getElementById('openBtn');
        const closeBtn = document.getElementById('closeBtn');
        const destroyBtn = document.getElementById('destroyBtn');

        // Update status display
        function updateStatus() {
            const isOpen = isInitialized && HalalChatWidget.isOpen();
            statusEl.textContent = isInitialized 
                ? (isOpen ? 'Widget Status: Open' : 'Widget Status: Closed')
                : 'Widget Status: Not Initialized';
            statusEl.className = isInitialized 
                ? (isOpen ? 'status open' : 'status closed')
                : 'status closed';
            
            // Update button states
            initBtn.disabled = isInitialized;
            openBtn.disabled = !isInitialized;
            closeBtn.disabled = !isInitialized;
            destroyBtn.disabled = !isInitialized;
        }

        // Initialize widget
        initBtn.addEventListener('click', () => {
            HalalChatWidget.init({
                baseURL: 'http://localhost:16001',
                botSlug: 'halal-assistant',
                position: 'bottom-right',
                theme: 'light',
                language: 'en'
            });
            isInitialized = true;
            updateStatus();
        });

        // Open chat
        openBtn.addEventListener('click', () => {
            HalalChatWidget.open();
            updateStatus();
        });

        // Close chat
        closeBtn.addEventListener('click', () => {
            HalalChatWidget.close();
            updateStatus();
        });

        // Destroy widget
        destroyBtn.addEventListener('click', () => {
            HalalChatWidget.destroy();
            isInitialized = false;
            updateStatus();
        });

        // Initial status update
        updateStatus();

        // Periodically update status (in case user interacts with widget directly)
        setInterval(updateStatus, 1000);
    </script>
</body>
</html>

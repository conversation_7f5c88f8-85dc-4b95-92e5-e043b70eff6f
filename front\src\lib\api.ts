// API service functions for the Halal Malaysia Portal

import { useAuthStore } from '@/stores/auth';
import type {
  // Admin Types
  AdminUser,
  AdminUserCreationRequest,
  AdminUserResponse,
  AdminUserUpdateRequest,
  Announcement,
  ApiResponse,
  Collection,
  CollectionCreateRequest,
  CollectionUpdateRequest,
  DocumentResponse,
  NewsItem,
  PaginatedResponse,
  S3ConfigurationCreationRequest,
  S3ConfigurationPreset,
  S3ConfigurationResponse,
  S3ConfigurationUpdateRequest,
  SearchParams,
  SearchResult,
  ServiceConfiguration,
  ServiceCreationRequest,
  ServiceUpdateRequest,
} from '@/types';
import type { Document, DocumentCreateRequest, DocumentUpdateRequest, DocumentUploadResponse } from '@/types/document';

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:16001';
const DEFAULT_SITE_ID = process.env.NEXT_PUBLIC_DEFAULT_SITE_ID || '1';

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {},
): Promise<ApiResponse<T>> {
  try {
    // Use frontend proxy for admin endpoints to avoid CORS issues
    const isAdminEndpoint = endpoint.startsWith('/admin');
    const isLoginEndpoint = endpoint.includes('/login');

    let url: string;
    if (isAdminEndpoint) {
      // Use frontend proxy for admin endpoints
      url = `/api${endpoint}`;
    } else if (isLoginEndpoint) {
      // Use backend directly for non-admin login endpoints
      url = `${API_BASE_URL}/api${endpoint}`;
    } else {
      // Use backend directly with siteId prefix for other endpoints
      url = `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}${endpoint}`;
    }

    // Get token from Zustand store
    const token =
      typeof window !== 'undefined' ? useAuthStore.getState().adminToken : null;
    console.log('🔑 apiRequest token:', {
      endpoint,
      token: token ? `${token.substring(0, 20)}...` : 'null',
      url,
      isAdminEndpoint
    });
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // For FormData, Content-Type should be set by browser, so remove it
    if (options.body instanceof FormData) {
      delete headers['Content-Type'];
    }

    const response = await fetch(url, {
      ...options, // Spread options first
      headers, // Then override headers
    });

    // Handle cases where response might not be JSON (e.g. 204 No Content for DELETE)
    if (response.status === 204) {
      // For a 204, there's no content, so we construct a success response.
      // The exact structure depends on how consuming code expects ApiResponse<void>.
      // Assuming ApiResponse<void> might look like { success: true } or just be an empty object.
      return { success: true } as unknown as ApiResponse<T>;
    }

    const data = await response.json();

    if (!response.ok) {
      console.error('API error response:', data);
      throw new Error(
        data.error || data.message || `HTTP error! status: ${response.status}`,
      );
    }

    return data;
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}

// Search API functions
export const searchAPI = {
  // Basic search
  search: async (
    params: SearchParams,
  ): Promise<ApiResponse<SearchResult[]>> => {
    const searchParams = new URLSearchParams();

    if (params.query) {
      searchParams.append('q', params.query);
    }
    if (params.status) {
      searchParams.append('status', params.status);
    }
    if (params.category) {
      searchParams.append('category', params.category);
    }
    if (params.country) {
      searchParams.append('country', params.country);
    }
    if (params.page) {
      searchParams.append('page', params.page.toString());
    }
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }
    if (params.sortBy) {
      searchParams.append('sortBy', params.sortBy);
    }
    if (params.sortOrder) {
      searchParams.append('sortOrder', params.sortOrder);
    }

    return apiRequest<SearchResult[]>(`/search?${searchParams.toString()}`);
  },

  // Advanced search with filters
  advancedSearch: async (
    query: string,
    filters: Record<string, any>,
  ): Promise<ApiResponse<any>> => {
    return apiRequest<any>('/search', {
      method: 'POST',
      body: JSON.stringify({ query, filters }),
    });
  },
};

// Announcements API functions
export const announcementsAPI = {
  // Get all announcements
  getAll: async (
    params: {
      category?: string;
      featured?: boolean;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {},
  ): Promise<ApiResponse<Announcement[]>> => {
    const searchParams = new URLSearchParams();

    if (params.category) {
      searchParams.append('category', params.category);
    }
    if (params.featured !== undefined) {
      searchParams.append('featured', params.featured.toString());
    }
    if (params.page) {
      searchParams.append('page', params.page.toString());
    }
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }
    if (params.sortBy) {
      searchParams.append('sortBy', params.sortBy);
    }
    if (params.sortOrder) {
      searchParams.append('sortOrder', params.sortOrder);
    }

    return apiRequest<Announcement[]>(
      `/announcements?${searchParams.toString()}`,
    );
  },

  // Get single announcement
  getById: async (id: string): Promise<ApiResponse<Announcement>> => {
    return apiRequest<Announcement>(`/announcements/${id}`);
  },

  // Create announcement
  create: async (
    announcement: Omit<Announcement, 'id' | 'date'>,
  ): Promise<ApiResponse<Announcement>> => {
    return apiRequest<Announcement>('/announcements', {
      method: 'POST',
      body: JSON.stringify(announcement),
    });
  },

  // Update announcement
  update: async (
    id: string,
    announcement: Partial<Announcement>,
  ): Promise<ApiResponse<Announcement>> => {
    return apiRequest<Announcement>(`/announcements/${id}`, {
      method: 'PUT',
      body: JSON.stringify(announcement),
    });
  },

  // Delete announcement
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/announcements/${id}`, {
      method: 'DELETE',
    });
  },
};

// News API functions
export const newsAPI = {
  // Get all news
  getAll: async (
    params: {
      category?: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {},
  ): Promise<ApiResponse<NewsItem[]>> => {
    const searchParams = new URLSearchParams();

    if (params.category) {
      searchParams.append('category', params.category);
    }
    if (params.search) {
      searchParams.append('search', params.search);
    }
    if (params.page) {
      searchParams.append('page', params.page.toString());
    }
    if (params.limit) {
      searchParams.append('limit', params.limit.toString());
    }
    if (params.sortBy) {
      searchParams.append('sortBy', params.sortBy);
    }
    if (params.sortOrder) {
      searchParams.append('sortOrder', params.sortOrder);
    }

    return apiRequest<NewsItem[]>(`/news?${searchParams.toString()}`);
  },

  // Get single news item
  getById: async (id: string): Promise<ApiResponse<NewsItem>> => {
    return apiRequest<NewsItem>(`/news/${id}`);
  },

  // Create news item
  create: async (
    newsItem: Omit<NewsItem, 'id' | 'date'>,
  ): Promise<ApiResponse<NewsItem>> => {
    return apiRequest<NewsItem>('/news', {
      method: 'POST',
      body: JSON.stringify(newsItem),
    });
  },

  // Update news item
  update: async (
    id: string,
    newsItem: Partial<NewsItem>,
  ): Promise<ApiResponse<NewsItem>> => {
    return apiRequest<NewsItem>(`/news/${id}`, {
      method: 'PUT',
      body: JSON.stringify(newsItem),
    });
  },

  // Delete news item
  delete: async (id: string): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/news/${id}`, {
      method: 'DELETE',
    });
  },
};

// Utility functions for API responses
export const apiUtils = {
  // Handle API errors
  handleError: (error: any): string => {
    if (error.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unexpected error occurred';
  },

  // Format API response for UI with backward compatibility
  formatResponse: <T>(response: ApiResponse<T>) => {
    // Handle standardized format
    if (typeof response.success === 'boolean') {
      return {
        success: response.success,
        data: response.data,
        error: response.error,
        message: response.message,
        pagination: response.pagination,
      };
    }

    // Handle legacy format - if no success field, assume success unless there's an error
    const hasError = response.error || (response as any).status === 'error';
    return {
      success: !hasError,
      data: response.data || response, // For legacy responses, the whole response might be the data
      error: response.error,
      message: response.message,
      pagination: response.pagination,
    };
  },

  // Check if response is successful (with backward compatibility)
  isSuccess: <T>(
    response: ApiResponse<T>,
  ): response is ApiResponse<T> & { success: true } => {
    // Handle standardized format
    if (typeof response.success === 'boolean') {
      return response.success === true;
    }

    // Handle legacy format - if no success field, assume success unless there's an error
    return !response.error && (response as any).status !== 'error';
  },
};

// React Query keys for caching
export const queryKeys = {
  announcements: {
    all: ['announcements'] as const,
    lists: () => [...queryKeys.announcements.all, 'list'] as const,
    list: (filters: Record<string, any>) =>
      [...queryKeys.announcements.lists(), filters] as const,
    details: () => [...queryKeys.announcements.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.announcements.details(), id] as const,
  },
  news: {
    all: ['news'] as const,
    lists: () => [...queryKeys.news.all, 'list'] as const,
    list: (filters: Record<string, any>) =>
      [...queryKeys.news.lists(), filters] as const,
    details: () => [...queryKeys.news.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.news.details(), id] as const,
  },
  search: {
    all: ['search'] as const,
    results: (params: SearchParams) =>
      [...queryKeys.search.all, 'results', params] as const,
  },
};

// Chat Session Types
interface ChatMessage {
  id: string;
  role: 'system' | 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: string;
  agentId?: number;
  agentName?: string;
  imageUrl?: string;
  audioUrl?: string;
  fileUrl?: string;
  fileName?: string;
}

interface ChatSession {
  id: string;
  userId?: string;
  platform: string;
  platformId?: string;
  status: string;
  isHandedOver: boolean;
  createdAt: string;
  lastMessageAt?: string;
  messages: ChatMessage[];
  agentId?: number;
  agentName?: string;
  messageCount?: number;
}

interface HandoverRequest {
  id: number;
  sessionId: string;
  requestedBy: string;
  reason?: string;
  priority: 'low' | 'normal' | 'high';
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
}

interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  pendingHandovers: number;
  completedToday: number;
}

// Export default API object
export const api = {
  // Generic HTTP methods
  get: <T = any>(endpoint: string): Promise<ApiResponse<T>> =>
    apiRequest<T>(endpoint),

  post: <T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),

  put: <T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),

  delete: <T = any>(endpoint: string): Promise<ApiResponse<T>> =>
    apiRequest<T>(endpoint, { method: 'DELETE' }),

  patch: <T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> =>
    apiRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }),

  search: searchAPI,
  announcements: announcementsAPI,
  news: newsAPI,
  // Placeholder for admin related API calls
  admin: {
    // Admin User Management
    getMe: (): Promise<ApiResponse<{ user: AdminUser }>> =>
      apiRequest<{ user: AdminUser }>('/admin/me'),
    listUsers: (): Promise<ApiResponse<AdminUserResponse[]>> =>
      apiRequest<AdminUserResponse[]>('/admin/users'),
    getUserById: (id: number): Promise<ApiResponse<AdminUserResponse>> =>
      apiRequest<AdminUserResponse>(`/admin/users/${id}`),
    createUser: (
      data: AdminUserCreationRequest,
    ): Promise<ApiResponse<AdminUserResponse>> =>
      apiRequest<AdminUserResponse>('/admin/users', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateUser: (
      id: number,
      data: AdminUserUpdateRequest,
    ): Promise<ApiResponse<AdminUserResponse>> =>
      apiRequest<AdminUserResponse>(`/admin/users/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteUser: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/users/${id}`, { method: 'DELETE' }),

    // Chat Session Management
    getSessions: (): Promise<ApiResponse<ChatSession[]>> =>
      apiRequest<ChatSession[]>('/admin/sessions'),
    getSession: (sessionId: string): Promise<ApiResponse<ChatSession>> =>
      apiRequest<ChatSession>(`/admin/sessions/${sessionId}`),
    sendMessage: (
      sessionId: string,
      data: {
        content: string;
        type: string;
        fileUrl?: string;
        fileName?: string;
        audioUrl?: string;
      },
    ): Promise<ApiResponse<{ messageId: string }>> =>
      apiRequest<{ messageId: string }>(
        `/admin/sessions/${sessionId}/message`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        },
      ),
    completeSession: (sessionId: string): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/sessions/${sessionId}/complete`, {
        method: 'POST',
      }),
    getHandoverRequests: (): Promise<ApiResponse<HandoverRequest[]>> =>
      apiRequest<HandoverRequest[]>('/admin/sessions/handovers/pending'),
    getSessionStats: (): Promise<ApiResponse<SessionStats>> =>
      apiRequest<SessionStats>('/admin/sessions/stats'),

    // S3 Configuration Management
    listS3Configurations: (): Promise<ApiResponse<S3ConfigurationResponse[]>> =>
      apiRequest<S3ConfigurationResponse[]>('/admin/s3-configurations'),
    getS3ConfigurationById: (
      id: number,
    ): Promise<ApiResponse<S3ConfigurationResponse>> =>
      apiRequest<S3ConfigurationResponse>(`/admin/s3-configurations/${id}`),
    createS3Configuration: (
      data: S3ConfigurationCreationRequest,
    ): Promise<ApiResponse<S3ConfigurationResponse>> =>
      apiRequest<S3ConfigurationResponse>('/admin/s3-configurations', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateS3Configuration: (
      id: number,
      data: S3ConfigurationUpdateRequest,
    ): Promise<ApiResponse<S3ConfigurationResponse>> =>
      apiRequest<S3ConfigurationResponse>(`/admin/s3-configurations/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteS3Configuration: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/s3-configurations/${id}`, { method: 'DELETE' }),
    getS3Presets: (): Promise<
      ApiResponse<Record<string, S3ConfigurationPreset>>
    > =>
      // Backend uses S3ConfigurationPreset from server types
      apiRequest<Record<string, S3ConfigurationPreset>>(
        '/admin/s3-configurations/presets',
      ),

    // Collection Management
    getCollections: (): Promise<ApiResponse<Collection[]>> =>
      apiRequest<Collection[]>('/admin/collections'),
    getCollectionById: (id: number): Promise<ApiResponse<Collection>> =>
      apiRequest<Collection>(`/admin/collections/${id}`),
    createCollection: (
      data: CollectionCreateRequest,
    ): Promise<ApiResponse<Collection>> =>
      apiRequest<Collection>('/admin/collections', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateCollection: (
      id: number,
      data: CollectionUpdateRequest,
    ): Promise<ApiResponse<Collection>> =>
      apiRequest<Collection>(`/admin/collections/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteCollection: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/collections/${id}`, { method: 'DELETE' }),

    // Document Management
    getDocuments: (
      page = 1,
      limit = 10,
    ): Promise<ApiResponse<PaginatedResponse<Document>>> =>
      apiRequest<PaginatedResponse<Document>>(
        `/admin/documents?page=${page}&limit=${limit}`,
      ),
    getDocumentsByCollection: (
      collectionId: number,
      page = 1,
      limit = 10,
    ): Promise<ApiResponse<PaginatedResponse<Document>>> =>
      apiRequest<PaginatedResponse<Document>>(
        `/admin/collections/${collectionId}/documents?page=${page}&limit=${limit}`,
      ),
    getDocumentById: (id: number): Promise<ApiResponse<Document>> =>
      apiRequest<Document>(`/admin/documents/${id}`),
    createDocument: (
      data: DocumentCreateRequest,
    ): Promise<ApiResponse<Document>> =>
      apiRequest<Document>('/admin/documents', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateDocument: (
      id: number,
      data: DocumentUpdateRequest,
    ): Promise<ApiResponse<Document>> =>
      apiRequest<Document>(`/admin/documents/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    uploadDocument: (
      formData: FormData,
      onProgress?: (progress: number) => void,
    ): Promise<ApiResponse<DocumentUploadResponse>> => {
      // For file uploads, we need to handle progress and use different headers
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        if (onProgress) {
          xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
              const progress = (e.loaded / e.total) * 100;
              onProgress(progress);
            }
          });
        }

        xhr.addEventListener('load', () => {
          try {
            const response = JSON.parse(xhr.responseText);
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve({ success: true, data: response });
            } else {
              resolve({ success: false, error: response.error || 'Upload failed' });
            }
          } catch (error) {
            resolve({ success: false, error: 'Invalid response format' });
          }
        });

        xhr.addEventListener('error', () => {
          resolve({ success: false, error: 'Network error' });
        });

        const token = typeof window !== 'undefined' ? useAuthStore.getState().adminToken : null;
        const url = `${API_BASE_URL}/api/sites/${DEFAULT_SITE_ID}/admin/documents/upload`;

        xhr.open('POST', url);
        if (token) {
          xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }
        xhr.send(formData);
      });
    },
    deleteDocument: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/documents/${id}`, { method: 'DELETE' }),

    // Service Management
    listServices: (): Promise<ApiResponse<ServiceConfiguration[]>> =>
      apiRequest<ServiceConfiguration[]>('/admin/services'),
    getServiceById: (id: number): Promise<ApiResponse<ServiceConfiguration>> =>
      apiRequest<ServiceConfiguration>(`/admin/services/${id}`),
    createService: (
      data: ServiceCreationRequest,
    ): Promise<ApiResponse<ServiceConfiguration>> =>
      apiRequest<ServiceConfiguration>('/admin/services', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateService: (
      id: number,
      data: ServiceUpdateRequest,
    ): Promise<ApiResponse<ServiceConfiguration>> =>
      apiRequest<ServiceConfiguration>(`/admin/services/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteService: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/services/${id}`, { method: 'DELETE' }),

    // Agent Management
    listAgents: (): Promise<ApiResponse<any[]>> =>
      apiRequest<any[]>('/admin/agents'),
    getAgentById: (id: number): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/agents/${id}`),
    createAgent: (data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>('/admin/agents', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateAgent: (id: number, data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/agents/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteAgent: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/agents/${id}`, { method: 'DELETE' }),

    // Agent Statistics
    getAgentStats: (): Promise<
      ApiResponse<{
        totalAgents: number;
        onlineAgents: number;
        activeSessions: number;
        pendingHandovers: number;
      }>
    > =>
      apiRequest<{
        totalAgents: number;
        onlineAgents: number;
        activeSessions: number;
        pendingHandovers: number;
      }>('/admin/agents/stats'),

    // Bot Management
    listBots: (): Promise<ApiResponse<any[]>> =>
      apiRequest<any[]>('/admin/bots'),
    getBotById: (id: number): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/bots/${id}`),
    createBot: (data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>('/admin/bots', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateBot: (id: number, data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/bots/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteBot: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/bots/${id}`, { method: 'DELETE' }),

    // Team Management
    listTeams: (): Promise<ApiResponse<any[]>> =>
      apiRequest<any[]>('/admin/teams'),
    getTeamById: (id: number): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/teams/${id}`),
    createTeam: (data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>('/admin/teams', {
        method: 'POST',
        body: JSON.stringify(data),
      }),
    updateTeam: (id: number, data: any): Promise<ApiResponse<any>> =>
      apiRequest<any>(`/admin/teams/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      }),
    deleteTeam: (id: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/teams/${id}`, { method: 'DELETE' }),

    // Team Assignment
    assignUserToTeam: (userId: number, teamId: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/users/${userId}/team`, {
        method: 'PUT',
        body: JSON.stringify({ teamId }),
      }),
    removeUserFromTeam: (userId: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/users/${userId}/team`, {
        method: 'DELETE',
      }),
    assignBotToTeam: (botId: number, teamId: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/bots/${botId}/team`, {
        method: 'PUT',
        body: JSON.stringify({ teamId }),
      }),
    removeBotFromTeam: (botId: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/admin/bots/${botId}/team`, {
        method: 'DELETE',
      }),
  },

  // Session Management APIs
  sessions: {
    // Get session statistics (with optional team filtering)
    getStats: (teamId?: number): Promise<
      ApiResponse<{
        totalSessions: number;
        activeSessions: number;
        pendingHandovers: number;
        completedToday: number;
      }>
    > => {
      const params = teamId ? `?teamId=${teamId}` : '';
      return apiRequest<{
        totalSessions: number;
        activeSessions: number;
        pendingHandovers: number;
        completedToday: number;
      }>(`/sessions/stats${params}`);
    },

    // Get pending handover requests (with optional team filtering)
    getPendingHandovers: (teamId?: number): Promise<ApiResponse<any[]>> => {
      const params = teamId ? `?teamId=${teamId}` : '';
      return apiRequest<any[]>(`/sessions/handovers/pending${params}`);
    },

    // Get active sessions (with optional team filtering)
    getActiveSessions: (teamId?: number): Promise<ApiResponse<any[]>> => {
      const params = teamId ? `?teamId=${teamId}` : '';
      return apiRequest<any[]>(`/sessions/active${params}`);
    },

    // Get all active sessions (with optional team filtering)
    getAllActiveSessions: (teamId?: number): Promise<ApiResponse<any[]>> => {
      const params = teamId ? `?teamId=${teamId}` : '';
      return apiRequest<any[]>(`/sessions/all-active${params}`);
    },

    // Get available agents for session assignment
    getAvailableAgents: (
      sessionId: string,
      enforceTeamConstraints: boolean = true
    ): Promise<ApiResponse<any[]>> => {
      const params = `?enforceTeamConstraints=${enforceTeamConstraints}`;
      return apiRequest<any[]>(`/sessions/${sessionId}/available-agents${params}`);
    },

    // Get available agents for handover request
    getAvailableAgentsForHandover: (
      requestId: number,
      enforceTeamConstraints: boolean = true
    ): Promise<ApiResponse<any[]>> => {
      const params = `?enforceTeamConstraints=${enforceTeamConstraints}`;
      return apiRequest<any[]>(`/sessions/handovers/${requestId}/available-agents${params}`);
    },

    // Assign handover request to agent
    assignHandover: (requestId: number): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/sessions/handovers/${requestId}/assign`, {
        method: 'POST',
      }),

    // Take over a session
    takeoverSession: (sessionId: string): Promise<ApiResponse<void>> =>
      apiRequest<void>(`/sessions/${sessionId}/takeover`, {
        method: 'POST',
      }),
  },
  utils: apiUtils,
  keys: queryKeys,
};

import { describe, expect, test, beforeAll, afterAll, beforeEach } from 'bun:test'
import DatabaseService from '../services/database'
import type { TeamCreationRequest, UserCreateRequest, BotCreateRequest } from '../types'

describe('Team Constraint Session Assignment', () => {
  let dbService: DatabaseService
  let testSiteId: number
  let testTeamId1: number
  let testTeamId2: number
  let testAgentId1: number
  let testAgentId2: number
  let testBotId1: number
  let testBotId2: number
  let testSessionId: string

  beforeAll(async () => {
    // Initialize database service
    dbService = new DatabaseService()
    await dbService.initialize()
    
    // Use a test site ID (assuming site 1 exists)
    testSiteId = 1
  })

  beforeEach(async () => {
    // Create test teams
    const team1Data: TeamCreationRequest = {
      siteId: testSiteId,
      name: 'Team Alpha',
      description: 'First test team',
      isActive: true,
    }

    const team2Data: TeamCreationRequest = {
      siteId: testSiteId,
      name: 'Team Beta',
      description: 'Second test team',
      isActive: true,
    }

    const team1 = await dbService.createTeam(team1Data)
    const team2 = await dbService.createTeam(team2Data)
    testTeamId1 = team1.id
    testTeamId2 = team2.id

    // Create test agents in different teams
    const agent1Data: UserCreateRequest = {
      username: 'agent1',
      email: '<EMAIL>',
      password: 'password123',
      roles: ['AGENT'],
      isActive: true,
      teamId: testTeamId1,
    }

    const agent2Data: UserCreateRequest = {
      username: 'agent2',
      email: '<EMAIL>',
      password: 'password123',
      roles: ['AGENT'],
      isActive: true,
      teamId: testTeamId2,
    }

    const agent1 = await dbService.createUser(agent1Data, testSiteId)
    const agent2 = await dbService.createUser(agent2Data, testSiteId)
    testAgentId1 = agent1.id
    testAgentId2 = agent2.id

    // Create test bots in different teams
    const bot1Data: BotCreateRequest = {
      name: 'Bot Alpha',
      slug: 'bot-alpha',
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.5,
      isActive: true,
      teamId: testTeamId1,
    }

    const bot2Data: BotCreateRequest = {
      name: 'Bot Beta',
      slug: 'bot-beta',
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.5,
      isActive: true,
      teamId: testTeamId2,
    }

    const bot1 = await dbService.createBot(bot1Data, testSiteId)
    const bot2 = await dbService.createBot(bot2Data, testSiteId)
    testBotId1 = bot1.id
    testBotId2 = bot2.id

    // Create a test session with bot from team 1
    testSessionId = 'test-session-' + Date.now()
    await dbService.createChatSession({
      id: testSessionId,
      siteId: testSiteId,
      platform: 'web',
      botId: testBotId1,
    })
  })

  afterEach(async () => {
    // Clean up test data
    try {
      await dbService.deleteTeam(testTeamId1, testSiteId)
      await dbService.deleteTeam(testTeamId2, testSiteId)
    } catch (error) {
      // Ignore cleanup errors
    }
  })

  test('should allow agent from same team to take session', async () => {
    const result = await dbService.assignSessionToAgentWithTeamValidation(
      testSessionId,
      testAgentId1, // Agent from team 1, same as bot
      true // enforce team constraints
    )

    expect(result.success).toBe(true)
    expect(result.assignmentId).toBeDefined()
    expect(result.error).toBeUndefined()
  })

  test('should reject agent from different team when constraints enforced', async () => {
    const result = await dbService.assignSessionToAgentWithTeamValidation(
      testSessionId,
      testAgentId2, // Agent from team 2, different from bot (team 1)
      true // enforce team constraints
    )

    expect(result.success).toBe(false)
    expect(result.error).toContain('does not match session bot team')
    expect(result.assignmentId).toBeUndefined()
  })

  test('should allow agent from different team when constraints disabled', async () => {
    const result = await dbService.assignSessionToAgentWithTeamValidation(
      testSessionId,
      testAgentId2, // Agent from team 2, different from bot (team 1)
      false // disable team constraints
    )

    expect(result.success).toBe(true)
    expect(result.assignmentId).toBeDefined()
    expect(result.error).toBeUndefined()
  })

  test('should get available agents filtered by team', async () => {
    const availableAgents = await dbService.getAvailableAgentsForSession(
      testSessionId,
      testSiteId,
      true // enforce team constraints
    )

    // Should only return agents from team 1 (same as bot)
    expect(availableAgents.length).toBe(1)
    expect(availableAgents[0].id).toBe(testAgentId1)
  })

  test('should get all available agents when constraints disabled', async () => {
    const availableAgents = await dbService.getAvailableAgentsForSession(
      testSessionId,
      testSiteId,
      false // disable team constraints
    )

    // Should return agents from both teams
    expect(availableAgents.length).toBe(2)
    const agentIds = availableAgents.map(a => a.id)
    expect(agentIds).toContain(testAgentId1)
    expect(agentIds).toContain(testAgentId2)
  })

  test('should handle session without bot team constraints', async () => {
    // Create session without bot
    const sessionWithoutBot = 'test-session-no-bot-' + Date.now()
    await dbService.createChatSession({
      id: sessionWithoutBot,
      siteId: testSiteId,
      platform: 'web',
      // No botId
    })

    const result = await dbService.assignSessionToAgentWithTeamValidation(
      sessionWithoutBot,
      testAgentId2, // Any agent should work
      true // enforce team constraints
    )

    expect(result.success).toBe(true)
    expect(result.assignmentId).toBeDefined()
  })

  test('should reject assignment to inactive agent', async () => {
    // Deactivate agent
    await dbService.updateUser(testAgentId1, testSiteId, { isActive: false })

    const result = await dbService.assignSessionToAgentWithTeamValidation(
      testSessionId,
      testAgentId1,
      true
    )

    expect(result.success).toBe(false)
    expect(result.error).toContain('Agent is not active')
  })

  test('should reject assignment to non-existent agent', async () => {
    const result = await dbService.assignSessionToAgentWithTeamValidation(
      testSessionId,
      99999, // Non-existent agent ID
      true
    )

    expect(result.success).toBe(false)
    expect(result.error).toContain('Agent not found')
  })

  test('should reject assignment to non-existent session', async () => {
    const result = await dbService.assignSessionToAgentWithTeamValidation(
      'non-existent-session',
      testAgentId1,
      true
    )

    expect(result.success).toBe(false)
    expect(result.error).toContain('Session not found')
  })
})

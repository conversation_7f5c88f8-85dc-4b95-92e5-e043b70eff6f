'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, UserCog } from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { useTeamsStore } from '@/stores/teams'

export default function TeamDetailPage() {
  const params = useParams()
  const teamId = parseInt(params.id as string)
  
  const { currentTeam, isLoading, error, fetchTeamById, clearError } = useTeamsStore()

  useEffect(() => {
    if (teamId) {
      fetchTeamById(teamId)
    }
  }, [teamId, fetchTeamById])

  if (error) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-600 mb-4">{error}</p>
              <div className="flex gap-2">
                <Button onClick={() => fetchTeamById(teamId)} variant="outline">
                  Retry
                </Button>
                <Button onClick={clearError} variant="ghost">
                  Clear Error
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  if (isLoading || !currentTeam) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link href="/teams">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Teams
              </Button>
            </Link>
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold tracking-tight">{currentTeam.name}</h1>
                <Badge variant={currentTeam.isActive ? 'default' : 'secondary'}>
                  {currentTeam.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              {currentTeam.description && (
                <p className="text-muted-foreground">{currentTeam.description}</p>
              )}
            </div>
          </div>
          <Link href={`/teams/${teamId}/edit`}>
            <Button>
              <Edit className="mr-2 h-4 w-4" />
              Edit Team
            </Button>
          </Link>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Team Members */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCog className="h-5 w-5" />
                Team Members ({currentTeam.userCount || 0})
              </CardTitle>
              <CardDescription>
                Agents and users assigned to this team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!currentTeam.users || currentTeam.users.length === 0 ? (
                <div className="text-center py-8">
                  <UserCog className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No team members</h3>
                  <p className="text-muted-foreground mb-4">
                    No users are currently assigned to this team.
                  </p>
                  <Link href="/users">
                    <Button variant="outline" size="sm">
                      Manage Users
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-3">
                  {currentTeam.users.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">
                          {user.firstName && user.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user.username}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {user.email}
                        </div>
                        <div className="flex gap-1 mt-1">
                          {user.roles.map((role) => (
                            <Badge key={role} variant="outline" className="text-xs">
                              {role}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <Badge variant={user.isActive ? 'default' : 'secondary'}>
                        {user.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Team Bots */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Team Bots ({currentTeam.botCount || 0})
              </CardTitle>
              <CardDescription>
                Bots assigned to this team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!currentTeam.bots || currentTeam.bots.length === 0 ? (
                <div className="text-center py-8">
                  <Bot className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No team bots</h3>
                  <p className="text-muted-foreground mb-4">
                    No bots are currently assigned to this team.
                  </p>
                  <Link href="/bots">
                    <Button variant="outline" size="sm">
                      Manage Bots
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-3">
                  {currentTeam.bots.map((bot) => (
                    <div
                      key={bot.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">{bot.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {bot.provider} - {bot.model}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Temperature: {bot.temperature}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {bot.isDefault && (
                          <Badge variant="outline">Default</Badge>
                        )}
                        <Badge variant={bot.isActive ? 'default' : 'secondary'}>
                          {bot.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Team Info */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Team Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label className="text-sm font-medium">Created</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(currentTeam.createdAt).toLocaleString()}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Last Updated</Label>
                <p className="text-sm text-muted-foreground">
                  {new Date(currentTeam.updatedAt).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

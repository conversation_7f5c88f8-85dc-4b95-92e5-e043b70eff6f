import { create } from 'zustand'
import type { Bot } from '@/types'
import { useAuthStore } from './auth'

interface BotCreateRequest {
  name: string
  slug: string
  provider: string
  model: string
  temperature?: number
  isDefault?: boolean
  isActive?: boolean
  systemPrompt?: string
  teamId?: number | null
}

interface BotUpdateRequest {
  name?: string
  slug?: string
  provider?: string
  model?: string
  temperature?: number
  isDefault?: boolean
  isActive?: boolean
  systemPrompt?: string
  teamId?: number | null
}

interface BotsState {
  // State
  bots: Bot[]
  currentBot: Bot | null
  isLoading: boolean
  error: string | null
}

interface BotsActions {
  // Actions
  fetchBots: () => Promise<void>
  fetchBotById: (id: number) => Promise<Bot | null>
  createBot: (data: BotCreateRequest) => Promise<boolean>
  updateBot: (id: number, data: BotUpdateRequest) => Promise<boolean>
  deleteBot: (id: number) => Promise<boolean>
  setCurrentBot: (bot: Bot | null) => void
  clearError: () => void
}

type BotsStore = BotsState & BotsActions

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8787'

export const useBotsStore = create<BotsStore>((set, get) => ({
  // Initial state
  bots: [],
  currentBot: null,
  isLoading: false,
  error: null,

  // Actions
  fetchBots: async () => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch bots: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch bots')
      }

      set({
        bots: result.data || [],
        isLoading: false,
      })
    } catch (error) {
      console.error('Error fetching bots:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch bots',
        isLoading: false,
      })
    }
  },

  fetchBotById: async (id: number) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch bot: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch bot')
      }

      const bot = result.data as Bot
      set({ currentBot: bot, isLoading: false })
      return bot
    } catch (error) {
      console.error('Error fetching bot:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch bot',
        isLoading: false,
      })
      return null
    }
  },

  createBot: async (data: BotCreateRequest) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to create bot: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to create bot')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error creating bot:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to create bot',
        isLoading: false,
      })
      return false
    }
  },

  updateBot: async (id: number, data: BotUpdateRequest) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`Failed to update bot: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to update bot')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error updating bot:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to update bot',
        isLoading: false,
      })
      return false
    }
  },

  deleteBot: async (id: number) => {
    set({ isLoading: true, error: null })
    try {
      const { token } = useAuthStore.getState()
      if (!token) {
        throw new Error('No authentication token')
      }

      const response = await fetch(`${API_BASE_URL}/api/admin/bots/${id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to delete bot: ${response.statusText}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete bot')
      }

      set({ isLoading: false })
      return true
    } catch (error) {
      console.error('Error deleting bot:', error)
      set({
        error: error instanceof Error ? error.message : 'Failed to delete bot',
        isLoading: false,
      })
      return false
    }
  },

  setCurrentBot: (bot: Bot | null) => {
    set({ currentBot: bot })
  },

  clearError: () => {
    set({ error: null })
  },
}))

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat-Enabled Form Demo - Product Registration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 40px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }

        .form-section h3 {
            color: #4CAF50;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .checkbox-group, .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item, .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input, .radio-item input {
            width: auto;
            margin: 0;
        }

        .range-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .range-container input[type="range"] {
            flex: 1;
        }

        .range-value {
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }

        .color-preview {
            width: 50px;
            height: 50px;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin-left: 10px;
        }

        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 6px;
            padding: 30px;
            text-align: center;
            background: #f9f9f9;
            transition: border-color 0.3s ease;
        }

        .file-upload-area:hover {
            border-color: #4CAF50;
        }

        .submit-section {
            text-align: center;
            padding: 30px;
            background: #f5f5f5;
            border-top: 1px solid #e0e0e0;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .chat-instructions {
            background: #e3f2fd;
            border: 1px solid #2196F3;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .chat-instructions h4 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .chat-instructions ul {
            margin-left: 20px;
        }

        .chat-instructions li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .checkbox-group, .radio-group {
                flex-direction: column;
                gap: 10px;
            }
            
            .range-container {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Smart Product Registration</h1>
            <p>AI-Powered Form Assistant Demo</p>
        </div>

        <div class="form-container">
            <div class="chat-instructions">
                <h4>💬 Try the AI Assistant!</h4>
                <p>Use the chat widget to interact with this form. Try commands like:</p>
                <ul>
                    <li>"Fill in my <NAME_EMAIL>"</li>
                    <li>"What's currently in the name field?"</li>
                    <li>"Help me complete this form step by step"</li>
                    <li>"Set my age to 25 and select premium plan"</li>
                    <li>"What fields are still empty?"</li>
                </ul>
            </div>

            <form id="productRegistrationForm" novalidate>
                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3>👤 Personal Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">First Name *</label>
                            <input type="text" id="firstName" name="firstName" required placeholder="Enter your first name">
                        </div>
                        <div class="form-group">
                            <label for="lastName">Last Name *</label>
                            <input type="text" id="lastName" name="lastName" required placeholder="Enter your last name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" placeholder="+****************">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="birthDate">Date of Birth</label>
                            <input type="date" id="birthDate" name="birthDate">
                        </div>
                        <div class="form-group">
                            <label for="age">Age</label>
                            <input type="number" id="age" name="age" min="13" max="120" placeholder="25">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password *</label>
                        <input type="password" id="password" name="password" required placeholder="Create a secure password">
                    </div>
                </div>

                <!-- Product Preferences Section -->
                <div class="form-section">
                    <h3>🛍️ Product Preferences</h3>
                    
                    <div class="form-group">
                        <label for="productCategory">Product Category *</label>
                        <select id="productCategory" name="productCategory" required>
                            <option value="">Select a category</option>
                            <option value="electronics">Electronics</option>
                            <option value="clothing">Clothing & Fashion</option>
                            <option value="home">Home & Garden</option>
                            <option value="sports">Sports & Outdoors</option>
                            <option value="books">Books & Media</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="interests">Interests (Multi-select)</label>
                        <select id="interests" name="interests" multiple size="4">
                            <option value="technology">Technology</option>
                            <option value="fashion">Fashion</option>
                            <option value="fitness">Fitness</option>
                            <option value="cooking">Cooking</option>
                            <option value="travel">Travel</option>
                            <option value="music">Music</option>
                            <option value="gaming">Gaming</option>
                            <option value="photography">Photography</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Subscription Plan *</label>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="planBasic" name="subscriptionPlan" value="basic" required>
                                <label for="planBasic">Basic ($9.99/month)</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="planPremium" name="subscriptionPlan" value="premium" required>
                                <label for="planPremium">Premium ($19.99/month)</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="planPro" name="subscriptionPlan" value="pro" required>
                                <label for="planPro">Pro ($39.99/month)</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Notification Preferences</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="emailNotifications" name="notifications" value="email">
                                <label for="emailNotifications">Email Notifications</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="smsNotifications" name="notifications" value="sms">
                                <label for="smsNotifications">SMS Notifications</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="pushNotifications" name="notifications" value="push">
                                <label for="pushNotifications">Push Notifications</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="newsletter" name="notifications" value="newsletter">
                                <label for="newsletter">Weekly Newsletter</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customization Section -->
                <div class="form-section">
                    <h3>🎨 Customization</h3>
                    
                    <div class="form-group">
                        <label for="budgetRange">Budget Range: $<span id="budgetValue">500</span></label>
                        <div class="range-container">
                            <input type="range" id="budgetRange" name="budgetRange" min="0" max="2000" value="500" step="50">
                            <div class="range-value" id="budgetDisplay">$500</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="themeColor">Preferred Theme Color</label>
                        <div style="display: flex; align-items: center;">
                            <input type="color" id="themeColor" name="themeColor" value="#4CAF50">
                            <div class="color-preview" id="colorPreview"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="bio">Tell us about yourself</label>
                        <textarea id="bio" name="bio" rows="4" placeholder="Share your interests, goals, or anything else you'd like us to know..."></textarea>
                    </div>
                </div>

                <!-- File Upload Section -->
                <div class="form-section">
                    <h3>📎 File Upload</h3>
                    
                    <div class="form-group">
                        <label for="profilePicture">Profile Picture</label>
                        <div class="file-upload-area">
                            <input type="file" id="profilePicture" name="profilePicture" accept="image/*">
                            <p>Click to select or drag and drop an image</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="documents">Additional Documents</label>
                        <input type="file" id="documents" name="documents" multiple accept=".pdf,.doc,.docx,.txt">
                    </div>
                </div>

                <!-- Agreement Section -->
                <div class="form-section">
                    <h3>📋 Agreement</h3>
                    
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="termsAccepted" name="termsAccepted" required>
                            <label for="termsAccepted">I agree to the Terms of Service *</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="privacyAccepted" name="privacyAccepted" required>
                            <label for="privacyAccepted">I agree to the Privacy Policy *</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="marketingConsent" name="marketingConsent">
                            <label for="marketingConsent">I consent to receive marketing communications</label>
                        </div>
                    </div>
                </div>

                <div class="submit-section">
                    <button type="submit" class="btn" id="submitBtn">🚀 Complete Registration</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Halal Chat Widget Integration -->
    <!-- Try development version first -->
    <script type="module" src="/src/index.tsx"></script>
    <script>
        // Wait for the widget to be fully loaded
        function initializeWidget() {
            console.log('Attempting to initialize widget...');
            console.log('HalalChatWidget available:', typeof window.HalalChatWidget);

            if (typeof window.HalalChatWidget === 'undefined') {
                console.error('HalalChatWidget is not available');
                return;
            }

            try {
                window.HalalChatWidget.init({
                    baseURL: 'http://localhost:16001',
                    // botSlug: 'default-default', // Comment out to use default bot
                    position: 'bottom-right',
                    theme: 'light',
                    language: 'en',
                    enableFormTools: true // Enable form interaction tools
                });
                console.log('Widget initialized successfully');

                // Check if container was created
                setTimeout(() => {
                    const container = document.getElementById('halal-chat-widget-container');
                    console.log('Widget container created:', container !== null);
                    if (container) {
                        console.log('Container style:', container.getAttribute('style'));
                    }
                }, 100);

            } catch (error) {
                console.error('Widget initialization failed:', error);
            }
        }

        // Try multiple initialization strategies
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeWidget);
        } else {
            initializeWidget();
        }

        // Fallback: try again after a short delay
        setTimeout(initializeWidget, 500);

        // Form interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Budget range slider
            const budgetRange = document.getElementById('budgetRange');
            const budgetDisplay = document.getElementById('budgetDisplay');
            const budgetValue = document.getElementById('budgetValue');
            
            budgetRange.addEventListener('input', function() {
                const value = this.value;
                budgetDisplay.textContent = `$${value}`;
                budgetValue.textContent = value;
            });

            // Color picker preview
            const themeColor = document.getElementById('themeColor');
            const colorPreview = document.getElementById('colorPreview');
            
            function updateColorPreview() {
                colorPreview.style.backgroundColor = themeColor.value;
            }
            
            themeColor.addEventListener('change', updateColorPreview);
            updateColorPreview(); // Initial update

            // Form submission
            const form = document.getElementById('productRegistrationForm');
            const submitBtn = document.getElementById('submitBtn');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                submitBtn.disabled = true;
                submitBtn.textContent = '⏳ Processing...';
                
                // Simulate form submission
                setTimeout(() => {
                    alert('🎉 Registration completed successfully!\n\nThis is a demo form. In a real application, the data would be submitted to a server.');
                    submitBtn.disabled = false;
                    submitBtn.textContent = '🚀 Complete Registration';
                }, 2000);
            });

            // Real-time validation feedback
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    if (this.checkValidity()) {
                        this.style.borderColor = '#4CAF50';
                    } else {
                        this.style.borderColor = '#f44336';
                    }
                });
            });
        });
    </script>
</body>
</html>

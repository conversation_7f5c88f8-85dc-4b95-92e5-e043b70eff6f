import { ApiResponse, Message, IntegrationStatus, FormToolExecutionResponse } from './types';

export class ChatAPI {
  private baseURL: string;
  private botSlug?: string;
  private defaultSiteId: string = '1';

  constructor(baseURL: string, botSlug?: string) {
    this.baseURL = baseURL.replace(/\/$/, ''); // Remove trailing slash
    this.botSlug = botSlug;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.error || `HTTP error! status: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
        ...data, // Include any additional fields from the response
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  async createSession(): Promise<ApiResponse<{ sessionId: string }>> {
    const body: any = {};

    if (this.botSlug) {
      // First get bot info
      const botResponse = await this.makeRequest<any>(
        `/api/sites/${this.defaultSiteId}/bots/slug/${this.botSlug}`
      );
      if (botResponse.success && botResponse.data) {
        body.botId = botResponse.data.id;
      }
    }

    return this.makeRequest<{ sessionId: string }>('/api/chat/session', {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async sendMessage(
    sessionId: string,
    message: string,
    model?: string
  ): Promise<ApiResponse<{ message: string; answer?: string; sources?: any[] }>> {
    const endpoint = this.botSlug
      ? `/api/sites/${this.defaultSiteId}/bots/${this.botSlug}/chat/message`
      : '/api/chat/message';

    const body: any = {
      sessionId,
      message,
      ...(this.botSlug && { botSlug: this.botSlug }),
      ...(model && { model }),
    };

    return this.makeRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async sendImage(
    sessionId: string,
    imageUrl: string,
    prompt?: string,
    model?: string
  ): Promise<ApiResponse<{ message: string; answer?: string; sources?: any[] }>> {
    const endpoint = this.botSlug
      ? `/api/sites/${this.defaultSiteId}/bots/${this.botSlug}/chat/image`
      : '/api/chat/image';

    const body: any = {
      sessionId,
      imageUrl,
      prompt: prompt || "What's in this image?",
      ...(this.botSlug && { botSlug: this.botSlug }),
      ...(model && { model }),
    };

    return this.makeRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }

  async uploadFile(file: File): Promise<ApiResponse<{
    type: string;
    url?: string;
    transcription?: string;
    originalFilename: string;
  }>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${this.baseURL}/api/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.error || `Upload failed: ${response.status}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('Upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  async getIntegrationStatus(): Promise<ApiResponse<IntegrationStatus>> {
    const whatsappResponse = await this.makeRequest<{
      whatsappEnabled: boolean;
      phoneNumber: string | null;
    }>('/api/chat/whatsapp-status');

    const facebookResponse = await this.makeRequest<{
      facebookEnabled: boolean;
      pageId: string | null;
    }>('/api/chat/facebook-status');

    return {
      success: true,
      data: {
        whatsappEnabled: whatsappResponse.data?.whatsappEnabled || false,
        phoneNumber: whatsappResponse.data?.phoneNumber || null,
        facebookEnabled: facebookResponse.data?.facebookEnabled || false,
        pageId: facebookResponse.data?.pageId || null,
      },
    };
  }

  async requestHandover(sessionId: string): Promise<ApiResponse<void>> {
    return this.makeRequest<void>(`/api/chat/session/${sessionId}/handover`, {
      method: 'POST',
    });
  }

  async sendToolResults(
    sessionId: string,
    toolResults: FormToolExecutionResponse[]
  ): Promise<ApiResponse<{ message: string; answer?: string; sources?: any[] }>> {
    const endpoint = this.botSlug
      ? `/api/sites/${this.defaultSiteId}/bots/${this.botSlug}/chat/tool-results`
      : '/api/chat/tool-results';

    const body: any = {
      sessionId,
      toolResults,
      ...(this.botSlug && { botSlug: this.botSlug }),
    };

    return this.makeRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
    });
  }
}

import { useState, useCallback } from 'react';
import { UploadedImage } from './types';
import { ChatAPI } from './api';

interface ImageUploadState {
  isUploading: boolean;
  error: string | null;
  uploadedImage: UploadedImage | null;
}

interface UseImageUploadProps {
  api: ChatAPI;
  onImageUploaded?: (image: UploadedImage) => void;
  maxFileSize?: number; // in bytes
}

export function useImageUpload({
  api,
  onImageUploaded,
  maxFileSize = 10 * 1024 * 1024, // 10MB default
}: UseImageUploadProps) {
  const [state, setState] = useState<ImageUploadState>({
    isUploading: false,
    error: null,
    uploadedImage: null,
  });

  const validateFile = useCallback(
    (file: File): string | null => {
      // Check file type
      const allowedTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
      ];
      if (!allowedTypes.includes(file.type)) {
        return 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.';
      }

      // Check file size
      if (file.size > maxFileSize) {
        const maxSizeMB = maxFileSize / (1024 * 1024);
        return `File size exceeds ${maxSizeMB}MB limit.`;
      }

      return null;
    },
    [maxFileSize]
  );

  const uploadImage = useCallback(
    async (file: File): Promise<UploadedImage | null> => {
      try {
        setState(prev => ({ ...prev, isUploading: true, error: null }));

        // Validate file
        const validationError = validateFile(file);
        if (validationError) {
          setState(prev => ({
            ...prev,
            error: validationError,
            isUploading: false,
          }));
          return null;
        }

        // Upload to server
        const response = await api.uploadFile(file);

        if (!response.success) {
          setState(prev => ({
            ...prev,
            error: response.error || 'Upload failed',
            isUploading: false,
          }));
          return null;
        }

        if (response.data?.type !== 'image' || !response.data?.url) {
          setState(prev => ({
            ...prev,
            error: 'Invalid response from server',
            isUploading: false,
          }));
          return null;
        }

        const uploadedImage: UploadedImage = {
          url: response.data.url,
          file,
        };

        setState(prev => ({
          ...prev,
          uploadedImage,
          isUploading: false,
        }));

        if (onImageUploaded) {
          onImageUploaded(uploadedImage);
        }

        return uploadedImage;
      } catch (error) {
        console.error('Failed to upload image:', error);
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to upload image';
        setState(prev => ({
          ...prev,
          error: errorMessage,
          isUploading: false,
        }));
        return null;
      }
    },
    [api, validateFile, onImageUploaded]
  );

  const uploadMultipleImages = useCallback(
    async (files: File[]): Promise<UploadedImage[]> => {
      const results: UploadedImage[] = [];

      for (const file of files) {
        const result = await uploadImage(file);
        if (result) {
          results.push(result);
        }
      }

      return results;
    },
    [uploadImage]
  );

  const clearImage = useCallback(() => {
    setState(prev => ({ ...prev, uploadedImage: null, error: null }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    uploadImage,
    uploadMultipleImages,
    clearImage,
    clearError,
    validateFile,
  };
}

// Drag and drop hook
interface UseDragAndDropProps {
  onDrop: (files: File[]) => void;
  accept?: string[];
}

export function useDragAndDrop({ onDrop, accept = ['image/*'] }: UseDragAndDropProps) {
  const [isDragActive, setIsDragActive] = useState(false);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Only set to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragActive(false);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      
      // Filter files by accepted types
      const acceptedFiles = files.filter(file => {
        return accept.some(acceptType => {
          if (acceptType === 'image/*') {
            return file.type.startsWith('image/');
          }
          return file.type === acceptType;
        });
      });

      if (acceptedFiles.length > 0) {
        onDrop(acceptedFiles);
      }
    },
    [onDrop, accept]
  );

  const getRootProps = useCallback(() => ({
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave,
    onDragOver: handleDragOver,
    onDrop: handleDrop,
  }), [handleDragEnter, handleDragLeave, handleDragOver, handleDrop]);

  const getInputProps = useCallback(() => ({
    type: 'file' as const,
    multiple: true,
    accept: accept.join(','),
    style: { display: 'none' },
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        onDrop(files);
      }
      // Reset input value to allow selecting the same file again
      e.target.value = '';
    },
  }), [onDrop, accept]);

  return {
    isDragActive,
    getRootProps,
    getInputProps,
  };
}

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.tsx'),
      name: 'HalalChatWidget',
      fileName: 'halal-chat-widget',
      formats: ['umd', 'es']
    },
    rollupOptions: {
      // Bundle everything for embeddable widget - don't externalize dependencies
      external: [],
      output: {
        globals: {}
      }
    },
    // Ensure the build is optimized for embedding
    minify: 'esbuild',
    sourcemap: false
  },
  // Development server configuration
  server: {
    port: 9000,
    open: true
  },
  // Ensure proper resolution for TypeScript
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx']
  }
})

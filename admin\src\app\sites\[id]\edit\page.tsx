'use client'

import { <PERSON><PERSON><PERSON><PERSON>, Plus, X } from 'lucide-react'
import Link from 'next/link'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useSitesStore } from '@/stores/sites'

export default function EditSitePage() {
  const router = useRouter()
  const params = useParams()
  const siteId = Number(params.id)

  const {
    currentSite,
    fetchSiteById,
    updateSite,
    isLoading,
    error,
    clearError,
  } = useSitesStore()

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    domains: [''],
    status: true,
  })

  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    if (siteId && !isNaN(siteId)) {
      fetchSiteById(siteId)
    }
  }, [siteId, fetchSiteById])

  useEffect(() => {
    if (currentSite && !isInitialized) {
      setFormData({
        name: currentSite.name,
        code: currentSite.code,
        domains: currentSite.domains.length > 0 ? currentSite.domains : [''],
        status: currentSite.status,
      })
      setIsInitialized(true)
    }
  }, [currentSite, isInitialized])

  const handleInputChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (field === 'status') {
        setFormData(prev => ({
          ...prev,
          [field]: e.target.checked,
        }))
      } else {
        setFormData(prev => ({
          ...prev,
          [field]: e.target.value,
        }))
      }
      // Clear error when user starts typing
      if (error) {
        clearError()
      }
    }

  const handleDomainChange = (index: number, value: string) => {
    const newDomains = [...formData.domains]
    newDomains[index] = value
    setFormData(prev => ({
      ...prev,
      domains: newDomains,
    }))
  }

  const addDomain = () => {
    setFormData(prev => ({
      ...prev,
      domains: [...prev.domains, ''],
    }))
  }

  const removeDomain = (index: number) => {
    if (formData.domains.length > 1) {
      const newDomains = formData.domains.filter((_, i) => i !== index)
      setFormData(prev => ({
        ...prev,
        domains: newDomains,
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate form
    if (!formData.name || !formData.code) {
      return
    }

    // Filter out empty domains
    const validDomains = formData.domains.filter(domain => domain.trim() !== '')
    if (validDomains.length === 0) {
      return
    }

    const success = await updateSite(siteId, {
      ...formData,
      domains: validDomains,
    })

    if (success) {
      router.push('/sites')
    }
  }

  const isFormValid =
    formData.name &&
    formData.code &&
    formData.domains.some(domain => domain.trim() !== '')

  if (!currentSite && !isLoading) {
    return (
      <DashboardLayout>
        <div className="py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900">Site Not Found</h1>
          <p className="mt-2 text-gray-600">
            The requested site could not be found.
          </p>
          <Link href="/sites">
            <Button className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sites
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/sites">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Sites
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Site</h1>
            <p className="text-gray-600">Update site information</p>
          </div>
        </div>

        {error && (
          <div className="rounded-md border border-red-200 bg-red-50 px-4 py-3 text-red-700">
            <div className="flex items-center justify-between">
              <span>{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                ×
              </Button>
            </div>
          </div>
        )}

        {isLoading && !isInitialized ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center">
                <p className="text-gray-500">Loading site information...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Site Information</CardTitle>
              <CardDescription>
                Update the details for this site
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Site Name *</Label>
                    <Input
                      id="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange('name')}
                      placeholder="Enter site name"
                      required
                      disabled={isLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="code">Site Code *</Label>
                    <Input
                      id="code"
                      type="text"
                      value={formData.code}
                      onChange={handleInputChange('code')}
                      placeholder="Enter unique site code"
                      required
                      disabled={isLoading}
                    />
                    <p className="text-xs text-gray-500">
                      Unique identifier for the site (e.g., "main", "selangor")
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Domains *</Label>
                  <div className="space-y-2">
                    {formData.domains.map((domain, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          type="text"
                          value={domain}
                          onChange={e =>
                            handleDomainChange(index, e.target.value)
                          }
                          placeholder="Enter domain (e.g., example.com)"
                          disabled={isLoading}
                          className="flex-1"
                        />
                        {formData.domains.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDomain(index)}
                            disabled={isLoading}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addDomain}
                    disabled={isLoading}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Domain
                  </Button>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    id="status"
                    type="checkbox"
                    checked={formData.status}
                    onChange={handleInputChange('status')}
                    disabled={isLoading}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="status">Active</Label>
                </div>

                <div className="flex items-center space-x-4 pt-4">
                  <Button type="submit" disabled={isLoading || !isFormValid}>
                    {isLoading ? 'Updating...' : 'Update Site'}
                  </Button>
                  <Link href="/sites">
                    <Button
                      type="button"
                      variant="outline"
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}

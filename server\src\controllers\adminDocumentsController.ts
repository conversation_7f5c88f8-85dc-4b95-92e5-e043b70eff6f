import { and, eq } from 'drizzle-orm'
import type { Context } from 'hono'
import { documents } from '@/db/schema'
import { DatabaseService } from '@/services/database'
import { S3Service } from '@/services/s3Service'

// Supported file types for document upload
const SUPPORTED_DOCUMENT_TYPES = [
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/pdf', // .pdf
  'text/plain', // .txt
  'text/markdown', // .md
  'text/asciidoc', // .asciidoc
  'audio/mpeg', // .mp3
  'audio/wav', // .wav
  'text/csv', // .csv
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
]

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

// Get all documents with pagination
export const getDocuments = async (c: Context) => {
  try {
    const siteId = c.get('siteId')
    const page = parseInt(c.req.query('page') || '1', 10)
    const limit = parseInt(c.req.query('limit') || '10', 10)

    const dbService = new DatabaseService(c.env)
    const result = await dbService.getAllDocuments(page, limit)

    return c.json({
      success: true,
      data: result,
    })
  } catch (error) {
    console.error('Error fetching documents:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to fetch documents',
      },
      500,
    )
  }
}

// Get documents by collection ID
export const getDocumentsByCollection = async (c: Context) => {
  try {
    const collectionId = parseInt(c.req.param('collectionId'), 10)
    const page = parseInt(c.req.query('page') || '1', 10)
    const limit = parseInt(c.req.query('limit') || '10', 10)

    if (isNaN(collectionId)) {
      return c.json(
        {
          success: false,
          error: 'Invalid collection ID',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const result = await dbService.getDocumentsByCollectionId(
      collectionId,
      page,
      limit,
    )

    return c.json({
      success: true,
      data: result,
    })
  } catch (error) {
    console.error('Error fetching documents by collection:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to fetch documents',
      },
      500,
    )
  }
}

// Get a single document by ID
export const getDocument = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10)

    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid document ID',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const document = await dbService.getDocumentById(id)

    if (!document) {
      return c.json(
        {
          success: false,
          error: 'Document not found',
        },
        404,
      )
    }

    return c.json({
      success: true,
      data: document,
    })
  } catch (error) {
    console.error('Error fetching document:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to fetch document',
      },
      500,
    )
  }
}

// Create a new document (metadata only)
export const createDocument = async (c: Context) => {
  try {
    const {
      collectionId,
      s3ConfigurationId,
      s3Key,
      filename,
      filesize,
      mimetype,
    } = await c.req.json()

    if (!collectionId || !s3ConfigurationId || !s3Key || !filename) {
      return c.json(
        {
          success: false,
          error:
            'Collection ID, S3 configuration ID, S3 key, and filename are required',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const newDocument = await dbService.createDocument({
      collectionId: parseInt(collectionId, 10),
      s3ConfigurationId: parseInt(s3ConfigurationId, 10),
      s3Key,
      filename,
      filesize: filesize ? parseInt(filesize, 10) : null,
      mimetype: mimetype || null,
    })

    if (!newDocument) {
      return c.json(
        {
          success: false,
          error: 'Failed to create document',
        },
        500,
      )
    }

    return c.json(
      {
        success: true,
        data: newDocument,
      },
      201,
    )
  } catch (error) {
    console.error('Error creating document:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to create document',
      },
      500,
    )
  }
}

// Update a document
export const updateDocument = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10)
    const { filename } = await c.req.json()

    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid document ID',
        },
        400,
      )
    }

    if (!filename) {
      return c.json(
        {
          success: false,
          error: 'Filename is required',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)
    const updatedDocument = await dbService.updateDocument(id, { filename })

    if (!updatedDocument) {
      return c.json(
        {
          success: false,
          error: 'Document not found or failed to update',
        },
        404,
      )
    }

    return c.json({
      success: true,
      data: updatedDocument,
    })
  } catch (error) {
    console.error('Error updating document:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to update document',
      },
      500,
    )
  }
}

// Delete a document
export const deleteDocument = async (c: Context) => {
  try {
    const id = parseInt(c.req.param('id'), 10)

    if (isNaN(id)) {
      return c.json(
        {
          success: false,
          error: 'Invalid document ID',
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)

    // Get document details before deletion for S3 cleanup
    const document = await dbService.getDocumentById(id)
    if (!document) {
      return c.json(
        {
          success: false,
          error: 'Document not found',
        },
        404,
      )
    }

    // Delete from database
    const success = await dbService.deleteDocument(id)

    if (!success) {
      return c.json(
        {
          success: false,
          error: 'Failed to delete document',
        },
        500,
      )
    }

    // TODO: Delete from S3 if needed
    // This would require getting S3 configuration and using S3Service
    // For now, we'll just delete the database record

    return c.json({
      success: true,
      message: 'Document deleted successfully',
    })
  } catch (error) {
    console.error('Error deleting document:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to delete document',
      },
      500,
    )
  }
}

// Upload a document file
export const uploadDocument = async (c: Context) => {
  try {
    const formData = await c.req.formData()
    const file = formData.get('file') as File
    const collectionId = formData.get('collectionId') as string

    if (!file) {
      return c.json(
        {
          success: false,
          error: 'No file uploaded',
        },
        400,
      )
    }

    if (!collectionId) {
      return c.json(
        {
          success: false,
          error: 'Collection ID is required',
        },
        400,
      )
    }

    // Validate file type
    if (!SUPPORTED_DOCUMENT_TYPES.includes(file.type)) {
      return c.json(
        {
          success: false,
          error: `Unsupported file type: ${file.type}. Supported types: ${SUPPORTED_DOCUMENT_TYPES.join(', ')}`,
        },
        400,
      )
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return c.json(
        {
          success: false,
          error: `File size exceeds maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
        },
        400,
      )
    }

    const dbService = new DatabaseService(c.env)

    // Verify collection exists
    const collection = await dbService.getCollectionById(
      parseInt(collectionId, 10),
    )
    if (!collection) {
      return c.json(
        {
          success: false,
          error: 'Collection not found',
        },
        404,
      )
    }

    // Get default S3 configuration (assuming site ID 1 for now)
    const s3Configs = await dbService.getAllS3Configurations()
    const defaultS3Config =
      s3Configs.find((config) => config.isDefault) || s3Configs[0]

    if (!defaultS3Config) {
      return c.json(
        {
          success: false,
          error: 'No S3 configuration found',
        },
        500,
      )
    }

    // Generate S3 key
    const timestamp = Date.now()
    const sanitizedFilename = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const s3Key = `collections/${collectionId}/${timestamp}_${sanitizedFilename}`

    // Initialize S3 service
    const s3Service = new S3Service(
      defaultS3Config.accessKeyId,
      defaultS3Config.secretAccessKey,
      defaultS3Config.region,
      defaultS3Config.bucketName,
    )

    // Convert file to buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer())

    // Upload to S3
    const uploadResult = await s3Service.uploadFile(
      fileBuffer,
      s3Key,
      file.type,
    )

    // Create document record in database
    const newDocument = await dbService.createDocument({
      collectionId: parseInt(collectionId, 10),
      s3ConfigurationId: defaultS3Config.id,
      s3Key,
      filename: file.name,
      filesize: file.size,
      mimetype: file.type,
    })

    if (!newDocument) {
      return c.json(
        {
          success: false,
          error: 'Failed to create document record',
        },
        500,
      )
    }

    return c.json(
      {
        success: true,
        data: {
          type: 'document',
          url: uploadResult.Location,
          s3Key,
          originalFilename: file.name,
          size: file.size,
          mimetype: file.type,
          document: newDocument,
        },
      },
      201,
    )
  } catch (error) {
    console.error('Error uploading document:', error)
    return c.json(
      {
        success: false,
        error: 'Failed to upload document',
      },
      500,
    )
  }
}

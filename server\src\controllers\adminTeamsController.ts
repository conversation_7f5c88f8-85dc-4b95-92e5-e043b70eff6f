import { and, eq } from 'drizzle-orm'
import type { Context } from 'hono'
import { teams } from '@/db/schema'
import DatabaseService from '@/services/database'

export const getTeams = async (c: Context) => {
  try {
    const db = c.get('db')
    const siteId = c.get('siteId')
    
    const allTeams = await db
      .select()
      .from(teams)
      .where(eq(teams.siteId, Number.parseInt(siteId)))
      .orderBy(teams.name)
    
    return c.json({ success: true, data: allTeams })
  } catch (error: any) {
    console.error('Error fetching teams:', error)
    return c.json(
      {
        success: false,
        data: null,
        error: 'Failed to fetch teams',
        message: error.message,
      },
      500,
    )
  }
}

export const getTeam = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const db = c.get('db')
    const siteId = c.get('siteId')
    
    const teamId = Number.parseInt(id)
    if (Number.isNaN(teamId)) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Invalid team ID',
        },
        400,
      )
    }
    
    const [team] = await db
      .select()
      .from(teams)
      .where(and(eq(teams.id, teamId), eq(teams.siteId, Number.parseInt(siteId))))
    
    if (!team) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Team not found',
        },
        404,
      )
    }
    
    return c.json({ success: true, data: team })
  } catch (error: any) {
    console.error(`Error fetching team ${c.req.param('id')}:`, error)
    return c.json(
      {
        success: false,
        data: null,
        error: 'Failed to fetch team',
        message: error.message,
      },
      500,
    )
  }
}

export const createTeam = async (c: Context) => {
  try {
    const { name, description, isActive } = await c.req.json()
    const siteId = c.get('siteId')
    
    if (!name) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Team name is required',
        },
        400,
      )
    }
    
    const db = c.get('db')
    const [newTeam] = await db
      .insert(teams)
      .values({
        name,
        description: description || null,
        isActive: isActive !== undefined ? isActive : true,
        siteId: Number.parseInt(siteId),
      })
      .returning()
    
    return c.json({ success: true, data: newTeam }, 201)
  } catch (error: any) {
    console.error('Error creating team:', error)
    if (error.message?.includes('already exists')) {
      return c.json(
        {
          success: false,
          data: null,
          error: error.message,
        },
        409,
      )
    }
    return c.json(
      {
        success: false,
        data: null,
        error: 'Failed to create team',
        message: error.message,
      },
      500,
    )
  }
}

export const updateTeam = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const { name, description, isActive } = await c.req.json()
    const db = c.get('db')
    const siteId = c.get('siteId')
    
    const teamId = Number.parseInt(id)
    if (Number.isNaN(teamId)) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Invalid team ID',
        },
        400,
      )
    }
    
    // Check if team exists
    const [existingTeam] = await db
      .select()
      .from(teams)
      .where(and(eq(teams.id, teamId), eq(teams.siteId, Number.parseInt(siteId))))
    
    if (!existingTeam) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Team not found',
        },
        404,
      )
    }
    
    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (isActive !== undefined) updateData.isActive = isActive
    updateData.updatedAt = new Date()
    
    const [updatedTeam] = await db
      .update(teams)
      .set(updateData)
      .where(and(eq(teams.id, teamId), eq(teams.siteId, Number.parseInt(siteId))))
      .returning()
    
    return c.json({ success: true, data: updatedTeam })
  } catch (error: any) {
    console.error(`Error updating team ${c.req.param('id')}:`, error)
    return c.json(
      {
        success: false,
        data: null,
        error: 'Failed to update team',
        message: error.message,
      },
      500,
    )
  }
}

export const deleteTeam = async (c: Context) => {
  try {
    const id = c.req.param('id')
    const db = c.get('db')
    const siteId = c.get('siteId')
    
    const teamId = Number.parseInt(id)
    if (Number.isNaN(teamId)) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Invalid team ID',
        },
        400,
      )
    }
    
    // Check if team exists
    const [existingTeam] = await db
      .select()
      .from(teams)
      .where(and(eq(teams.id, teamId), eq(teams.siteId, Number.parseInt(siteId))))
    
    if (!existingTeam) {
      return c.json(
        {
          success: false,
          data: null,
          error: 'Team not found',
        },
        404,
      )
    }
    
    const [deletedTeam] = await db
      .delete(teams)
      .where(and(eq(teams.id, teamId), eq(teams.siteId, Number.parseInt(siteId))))
      .returning()
    
    return c.json({ success: true, data: { message: 'Team deleted successfully' } })
  } catch (error: any) {
    console.error(`Error deleting team ${c.req.param('id')}:`, error)
    return c.json(
      {
        success: false,
        data: null,
        error: 'Failed to delete team',
        message: error.message,
      },
      500,
    )
  }
}
version: '3'

services:
  erpnext-redis-cache:
    image: redis:latest
    container_name: erpnext-redis-cache
    restart: unless-stopped
    volumes:
      - ./data/redis-cache:/data
    networks:
      - erpnext-network

  erpnext-redis-queue:
    image: redis:latest
    container_name: erpnext-redis-queue
    restart: unless-stopped
    volumes:
      - ./data/redis-queue:/data
    networks:
      - erpnext-network

  erpnext-redis-socketio:
    image: redis:latest
    container_name: erpnext-redis-socketio
    restart: unless-stopped
    volumes:
      - ./data/redis-socketio:/data
    networks:
      - erpnext-network

  erpnext-mariadb:
    image: mariadb:10.6
    container_name: erpnext-mariadb
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
    volumes:
      - ./data/mariadb:/var/lib/mysql
    networks:
      - erpnext-network

  erpnext-frappe:
    image: frappe/erpnext:v15.0.0
    container_name: erpnext-frappe
    restart: unless-stopped
    ports:
      - "9080:8000"
    environment:
      - DB_HOST=erpnext-mariadb
      - REDIS_CACHE_HOST=erpnext-redis-cache
      - REDIS_QUEUE_HOST=erpnext-redis-queue
      - REDIS_SOCKETIO_HOST=erpnext-redis-socketio
      - DB_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - SITE_NAME=${SITE_NAME}
    volumes:
      - ./data/sites:/home/<USER>/frappe-bench/sites
    networks:
      - erpnext-network
    depends_on:
      - erpnext-mariadb
      - erpnext-redis-cache
      - erpnext-redis-queue
      - erpnext-redis-socketio

networks:
  erpnext-network:
    driver: bridge

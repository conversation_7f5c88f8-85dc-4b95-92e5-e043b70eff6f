'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select } from '@/components/ui/select'
import { TeamSelect } from '@/components/ui/team-select'
import { useBotsStore } from '@/stores/bots'
import type { BotCreateRequest } from '@/types'

interface BotFormData {
  name: string
  slug: string
  provider: string
  model: string
  temperature: number
  isDefault: boolean
  isActive: boolean
  systemPrompt: string
  teamId: number | null
}

export default function NewBotPage() {
  const router = useRouter()
  const { createBot, isLoading, error, clearError } = useBotsStore()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<BotFormData>({
    defaultValues: {
      name: '',
      slug: '',
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.5,
      isDefault: false,
      isActive: true,
      systemPrompt: '',
      teamId: null,
    },
  })

  const isDefault = watch('isDefault')
  const isActive = watch('isActive')

  const onSubmit = async (data: BotFormData) => {
    setIsSubmitting(true)
    clearError()

    try {
      const botData: BotCreateRequest = {
        name: data.name.trim(),
        slug: data.slug.trim(),
        provider: data.provider,
        model: data.model,
        temperature: data.temperature,
        isDefault: data.isDefault,
        isActive: data.isActive,
        systemPrompt: data.systemPrompt.trim() || undefined,
        teamId: data.teamId,
      }

      const success = await createBot(botData)
      if (success) {
        router.push('/bots')
      }
    } catch (err) {
      console.error('Error creating bot:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex items-center gap-4 mb-6">
          <Link href="/bots">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Bots
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Bot</h1>
            <p className="text-muted-foreground">
              Configure a new AI bot for your applications
            </p>
          </div>
        </div>

        <div className="max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                Bot Configuration
              </CardTitle>
              <CardDescription>
                Enter the configuration details for the new bot
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Bot Name *</Label>
                    <Input
                      id="name"
                      {...register('name', {
                        required: 'Bot name is required',
                        minLength: {
                          value: 2,
                          message: 'Bot name must be at least 2 characters',
                        },
                      })}
                      placeholder="Enter bot name"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-red-500 text-sm">{errors.name.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="slug">Slug *</Label>
                    <Input
                      id="slug"
                      {...register('slug', {
                        required: 'Slug is required',
                        pattern: {
                          value: /^[a-z0-9-]+$/,
                          message: 'Slug must contain only lowercase letters, numbers, and hyphens',
                        },
                      })}
                      placeholder="bot-slug"
                      className={errors.slug ? 'border-red-500' : ''}
                    />
                    {errors.slug && (
                      <p className="text-red-500 text-sm">{errors.slug.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="provider">Provider *</Label>
                    <Select
                      id="provider"
                      {...register('provider', { required: 'Provider is required' })}
                      className={errors.provider ? 'border-red-500' : ''}
                    >
                      <option value="openai">OpenAI</option>
                      <option value="anthropic">Anthropic</option>
                      <option value="google">Google</option>
                    </Select>
                    {errors.provider && (
                      <p className="text-red-500 text-sm">{errors.provider.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="model">Model *</Label>
                    <Input
                      id="model"
                      {...register('model', { required: 'Model is required' })}
                      placeholder="gpt-4"
                      className={errors.model ? 'border-red-500' : ''}
                    />
                    {errors.model && (
                      <p className="text-red-500 text-sm">{errors.model.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="temperature">Temperature</Label>
                    <Input
                      id="temperature"
                      type="number"
                      step="0.1"
                      min="0"
                      max="2"
                      {...register('temperature', {
                        valueAsNumber: true,
                        min: { value: 0, message: 'Temperature must be at least 0' },
                        max: { value: 2, message: 'Temperature must be at most 2' },
                      })}
                      className={errors.temperature ? 'border-red-500' : ''}
                    />
                    {errors.temperature && (
                      <p className="text-red-500 text-sm">{errors.temperature.message}</p>
                    )}
                  </div>
                </div>

                <TeamSelect
                  value={watch('teamId')}
                  onChange={(teamId) => setValue('teamId', teamId)}
                  label="Team Assignment"
                  placeholder="Select a team (optional)"
                  required={false}
                />

                <div className="space-y-2">
                  <Label htmlFor="systemPrompt">System Prompt</Label>
                  <Textarea
                    id="systemPrompt"
                    {...register('systemPrompt')}
                    placeholder="Enter system prompt to define the bot's behavior..."
                    rows={4}
                  />
                  <p className="text-sm text-muted-foreground">
                    Optional: Define how the bot should behave and respond
                  </p>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isDefault"
                      checked={isDefault}
                      onCheckedChange={(checked) => setValue('isDefault', checked)}
                    />
                    <Label htmlFor="isDefault">Default Bot</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isActive"
                      checked={isActive}
                      onCheckedChange={(checked) => setValue('isActive', checked)}
                    />
                    <Label htmlFor="isActive">Active Bot</Label>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting || isLoading}
                    className="flex-1"
                  >
                    {isSubmitting || isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Create Bot
                      </>
                    )}
                  </Button>
                  <Link href="/bots">
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </Link>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}

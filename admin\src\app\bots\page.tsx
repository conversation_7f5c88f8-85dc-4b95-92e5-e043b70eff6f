'use client'

import { <PERSON><PERSON>, Edit, Plus, Search, Trash2 } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useBotsStore } from '@/stores/bots'
import { useTeamsStore } from '@/stores/teams'

export default function BotsPage() {
  const { bots, isLoading, error, fetchBots, deleteBot, clearError } = useBotsStore()
  const { teams, fetchTeams } = useTeamsStore()
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    fetchBots()
    fetchTeams() // Fetch teams to show team names
  }, [fetchBots, fetchTeams])

  const getTeamName = (teamId: number | null | undefined) => {
    if (!teamId) return null
    const team = teams.find(t => t.id === teamId)
    return team?.name || `Team ${teamId}`
  }

  const handleDelete = async (id: number, name: string) => {
    if (
      confirm(
        `Are you sure you want to delete the bot "${name}"? This action cannot be undone.`
      )
    ) {
      const success = await deleteBot(id)
      if (success) {
        // Refresh the list
        fetchBots()
      }
    }
  }

  const filteredBots = bots.filter(
    bot =>
      bot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bot.slug.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bot.provider.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bot.model.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (error) {
    return (
      <DashboardLayout>
        <div className="container mx-auto py-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-red-600 mb-4">{error}</p>
              <div className="flex gap-2">
                <Button onClick={() => fetchBots()} variant="outline">
                  Retry
                </Button>
                <Button onClick={clearError} variant="ghost">
                  Clear Error
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Bots</h1>
            <p className="text-muted-foreground">
              Manage AI bots and their configurations
            </p>
          </div>
          <Link href="/bots/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Bot
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Bots ({filteredBots.length})
            </CardTitle>
            <CardDescription>
              Configure and manage AI bots for your applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search bots..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>

            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredBots.length === 0 ? (
              <div className="text-center py-8">
                <Bot className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No bots found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery
                    ? 'No bots match your search criteria.'
                    : 'Get started by creating your first bot.'}
                </p>
                {!searchQuery && (
                  <Link href="/bots/new">
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Create Bot
                    </Button>
                  </Link>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredBots.map((bot) => (
                  <div
                    key={bot.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold">{bot.name}</h3>
                        <Badge variant="outline">{bot.slug}</Badge>
                        <Badge variant={bot.isActive ? 'default' : 'secondary'}>
                          {bot.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                        {bot.isDefault && (
                          <Badge variant="outline">Default</Badge>
                        )}
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">Provider:</span> {bot.provider}
                        </div>
                        <div>
                          <span className="font-medium">Model:</span> {bot.model}
                        </div>
                        <div>
                          <span className="font-medium">Temperature:</span> {bot.temperature}
                        </div>
                        {bot.teamId && (
                          <div>
                            <span className="font-medium">Team:</span> {getTeamName(bot.teamId)}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
                        <span>Created: {new Date(bot.createdAt).toLocaleDateString()}</span>
                        <span>Updated: {new Date(bot.updatedAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Link href={`/bots/${bot.id}/edit`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(bot.id, bot.name)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}

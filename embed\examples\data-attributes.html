<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contoh Integrasi Data Attributes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: ltr;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .hero {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .feature {
            margin: 20px 0;
            padding: 15px;
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hero">
            <h1>Selamat Datang ke Portal Halal</h1>
            <p>Contoh integrasi menggunakan data attributes untuk konfigurasi automatik</p>
        </div>

        <h2>Konfigurasi Menggunakan Data Attributes</h2>
        <p>Widget ini dikonfigurasikan menggunakan data attributes pada tag script:</p>

        <div class="code-example">
&lt;script 
  src="../dist/halal-chat-widget.js"
  data-base-url="http://localhost:16001"
  data-bot-slug="halal-assistant"
  data-position="bottom-left"
  data-theme="light"
  data-language="ms-MY"&gt;
&lt;/script&gt;
        </div>

        <div class="feature">
            <h3>🎯 Konfigurasi Mudah</h3>
            <p>Tidak perlu kod JavaScript tambahan - widget akan dikonfigurasikan secara automatik.</p>
        </div>

        <div class="feature">
            <h3>🌍 Bahasa Malaysia</h3>
            <p>Widget ini dikonfigurasikan untuk menggunakan Bahasa Malaysia sebagai bahasa antara muka.</p>
        </div>

        <div class="feature">
            <h3>📍 Posisi Kiri</h3>
            <p>Widget diletakkan di bahagian bawah kiri untuk demonstrasi.</p>
        </div>

        <p style="text-align: center; margin-top: 40px; color: #666;">
            Cari butang chat di sudut bawah kiri untuk bermula!
        </p>
    </div>

    <!-- Halal Chat Widget Integration with Data Attributes -->
    <script 
        src="../dist/halal-chat-widget.js"
        data-base-url="http://localhost:16001"
        data-bot-slug="halal-assistant"
        data-position="bottom-left"
        data-theme="light"
        data-language="ms-MY">
    </script>
</body>
</html>

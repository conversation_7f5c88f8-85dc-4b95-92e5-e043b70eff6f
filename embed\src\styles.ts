// Inline styles for complete style isolation
export const styles = {
  // Widget container
  widgetContainer: {
    position: 'fixed' as const,
    zIndex: 999999,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: '14px',
    lineHeight: '1.5',
    color: '#333',
    boxSizing: 'border-box' as const,
  },

  // Position variants
  bottomRight: {
    bottom: '20px',
    right: '20px',
  },

  bottomLeft: {
    bottom: '20px',
    left: '20px',
  },

  // Chat button (closed state)
  chatButton: {
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    backgroundColor: '#007bff',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
    transition: 'all 0.3s ease',
    outline: 'none',
  },

  chatButtonHover: {
    backgroundColor: '#0056b3',
    transform: 'scale(1.05)',
  },

  chatButtonDisabled: {
    backgroundColor: '#6c757d',
    cursor: 'not-allowed',
    opacity: 0.6,
  },

  // Notification dot
  notificationDot: {
    position: 'absolute' as const,
    top: '-2px',
    right: '-2px',
    width: '12px',
    height: '12px',
    backgroundColor: '#dc3545',
    borderRadius: '50%',
    animation: 'pulse 2s infinite',
  },

  // Chat window
  chatWindow: {
    width: '350px',
    height: '500px',
    backgroundColor: '#ffffff',
    borderRadius: '12px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden',
    border: '1px solid #e0e0e0',
  },

  chatWindowMaximized: {
    width: '90vw',
    height: '90vh',
    maxWidth: '800px',
    maxHeight: '600px',
  },

  // Header
  chatHeader: {
    backgroundColor: '#007bff',
    color: '#ffffff',
    padding: '12px 16px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottom: '1px solid #0056b3',
  },

  headerTitle: {
    fontSize: '16px',
    fontWeight: '600',
    margin: '0',
  },

  headerSubtitle: {
    fontSize: '12px',
    opacity: 0.9,
    margin: '0',
  },

  headerButtons: {
    display: 'flex',
    gap: '8px',
  },

  headerButton: {
    background: 'none',
    border: 'none',
    color: '#ffffff',
    cursor: 'pointer',
    padding: '4px',
    borderRadius: '4px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'background-color 0.2s',
    outline: 'none',
  },

  headerButtonHover: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },

  // Messages container
  messagesContainer: {
    flex: 1,
    overflowY: 'auto' as const,
    padding: '16px',
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '12px',
    backgroundColor: '#f8f9fa',
  },

  // Message bubble
  messageWrapper: {
    display: 'flex',
    marginBottom: '8px',
  },

  messageWrapperUser: {
    justifyContent: 'flex-end',
  },

  messageWrapperAssistant: {
    justifyContent: 'flex-start',
  },

  messageBubble: {
    maxWidth: '80%',
    padding: '12px 16px',
    borderRadius: '18px',
    wordWrap: 'break-word' as const,
    fontSize: '14px',
    lineHeight: '1.4',
  },

  messageBubbleUser: {
    backgroundColor: '#007bff',
    color: '#ffffff',
    borderBottomRightRadius: '6px',
  },

  messageBubbleAssistant: {
    backgroundColor: '#ffffff',
    color: '#333333',
    border: '1px solid #e0e0e0',
    borderBottomLeftRadius: '6px',
  },

  messageBubbleAgent: {
    backgroundColor: '#e8f5e8',
    color: '#2d5a2d',
    border: '1px solid #c3e6c3',
    borderBottomLeftRadius: '6px',
  },

  // Message metadata
  messageTimestamp: {
    fontSize: '11px',
    opacity: 0.7,
    marginTop: '4px',
    textAlign: 'right' as const,
  },

  messageImage: {
    width: '100%',
    maxWidth: '200px',
    height: 'auto',
    borderRadius: '8px',
    marginBottom: '8px',
  },

  // Agent info
  agentInfo: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '4px',
    fontSize: '12px',
    color: '#28a745',
    fontWeight: '500',
  },

  agentIcon: {
    width: '12px',
    height: '12px',
    marginRight: '4px',
  },

  // Input area
  inputArea: {
    padding: '16px',
    borderTop: '1px solid #e0e0e0',
    backgroundColor: '#ffffff',
  },

  inputForm: {
    display: 'flex',
    gap: '8px',
    alignItems: 'flex-end',
  },

  textInput: {
    flex: 1,
    border: '1px solid #d0d7de',
    borderRadius: '20px',
    padding: '10px 16px',
    fontSize: '14px',
    outline: 'none',
    resize: 'none' as const,
    minHeight: '20px',
    maxHeight: '100px',
    backgroundColor: '#ffffff',
    color: '#333333',
  },

  textInputFocus: {
    borderColor: '#007bff',
    boxShadow: '0 0 0 2px rgba(0, 123, 255, 0.25)',
  },

  // Buttons
  button: {
    border: 'none',
    borderRadius: '50%',
    width: '40px',
    height: '40px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s',
    outline: 'none',
  },

  sendButton: {
    backgroundColor: '#007bff',
    color: '#ffffff',
  },

  sendButtonHover: {
    backgroundColor: '#0056b3',
  },

  sendButtonDisabled: {
    backgroundColor: '#6c757d',
    cursor: 'not-allowed',
  },

  voiceButton: {
    backgroundColor: '#f8f9fa',
    color: '#6c757d',
    border: '1px solid #d0d7de',
  },

  voiceButtonActive: {
    backgroundColor: '#dc3545',
    color: '#ffffff',
  },

  voiceButtonHover: {
    backgroundColor: '#e9ecef',
  },

  // Loading indicator
  typingIndicator: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '12px 16px',
    backgroundColor: '#f8f9fa',
    borderRadius: '18px',
    maxWidth: '120px',
  },

  typingDots: {
    display: 'flex',
    gap: '2px',
  },

  typingDot: {
    width: '6px',
    height: '6px',
    backgroundColor: '#6c757d',
    borderRadius: '50%',
    animation: 'bounce 1.4s infinite ease-in-out',
  },

  // Error message
  errorMessage: {
    backgroundColor: '#f8d7da',
    color: '#721c24',
    border: '1px solid #f5c6cb',
    borderRadius: '8px',
    padding: '8px 12px',
    fontSize: '12px',
    marginTop: '8px',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  errorCloseButton: {
    background: 'none',
    border: 'none',
    color: '#721c24',
    cursor: 'pointer',
    padding: '0',
    marginLeft: '8px',
    outline: 'none',
  },

  // Drag and drop overlay
  dragOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    border: '2px dashed #007bff',
    borderRadius: '12px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '16px',
    color: '#007bff',
    fontWeight: '600',
    zIndex: 10,
  },

  // Uploaded image preview
  imagePreview: {
    position: 'relative' as const,
    display: 'inline-block',
    marginBottom: '8px',
  },

  imagePreviewImg: {
    width: '60px',
    height: '60px',
    objectFit: 'cover' as const,
    borderRadius: '8px',
    border: '1px solid #d0d7de',
  },

  imagePreviewClose: {
    position: 'absolute' as const,
    top: '-6px',
    right: '-6px',
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    backgroundColor: '#dc3545',
    color: '#ffffff',
    border: 'none',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    outline: 'none',
  },
};

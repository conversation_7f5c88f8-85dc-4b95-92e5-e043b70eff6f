import { describe, expect, test, beforeAll, afterAll } from 'bun:test'
import DatabaseService from '../services/database'
import type { TeamCreationRequest, TeamUpdateRequest } from '../types'

describe('Teams DatabaseService Methods', () => {
  let dbService: DatabaseService
  let testSiteId: number
  let testTeamId: number

  beforeAll(async () => {
    // Initialize database service
    dbService = new DatabaseService()
    await dbService.initialize()
    
    // Use a test site ID (assuming site 1 exists)
    testSiteId = 1
  })

  afterAll(async () => {
    // Clean up test data
    if (testTeamId) {
      await dbService.deleteTeam(testTeamId, testSiteId)
    }
  })

  test('should create a new team', async () => {
    const teamData: TeamCreationRequest = {
      siteId: testSiteId,
      name: 'Test Team',
      description: 'A test team for unit testing',
      isActive: true,
    }

    const newTeam = await dbService.createTeam(teamData)
    testTeamId = newTeam.id

    expect(newTeam).toBeDefined()
    expect(newTeam.name).toBe('Test Team')
    expect(newTeam.description).toBe('A test team for unit testing')
    expect(newTeam.isActive).toBe(true)
    expect(newTeam.siteId).toBe(testSiteId)
  })

  test('should get team by ID', async () => {
    const team = await dbService.getTeamById(testTeamId, testSiteId)

    expect(team).toBeDefined()
    expect(team?.id).toBe(testTeamId)
    expect(team?.name).toBe('Test Team')
  })

  test('should get teams by site', async () => {
    const teams = await dbService.getTeamsBySite(testSiteId)

    expect(teams).toBeDefined()
    expect(Array.isArray(teams)).toBe(true)
    expect(teams.length).toBeGreaterThan(0)
    
    const testTeam = teams.find(t => t.id === testTeamId)
    expect(testTeam).toBeDefined()
  })

  test('should update team', async () => {
    const updateData: TeamUpdateRequest = {
      name: 'Updated Test Team',
      description: 'Updated description',
      isActive: false,
    }

    const updatedTeam = await dbService.updateTeam(testTeamId, testSiteId, updateData)

    expect(updatedTeam).toBeDefined()
    expect(updatedTeam?.name).toBe('Updated Test Team')
    expect(updatedTeam?.description).toBe('Updated description')
    expect(updatedTeam?.isActive).toBe(false)
  })

  test('should get team with members', async () => {
    const teamWithMembers = await dbService.getTeamWithMembers(testTeamId, testSiteId)

    expect(teamWithMembers).toBeDefined()
    expect(teamWithMembers?.id).toBe(testTeamId)
    expect(teamWithMembers?.userCount).toBeDefined()
    expect(teamWithMembers?.botCount).toBeDefined()
    expect(Array.isArray(teamWithMembers?.users)).toBe(true)
    expect(Array.isArray(teamWithMembers?.bots)).toBe(true)
  })

  test('should delete team', async () => {
    const deleted = await dbService.deleteTeam(testTeamId, testSiteId)
    expect(deleted).toBe(true)

    // Verify team is deleted
    const team = await dbService.getTeamById(testTeamId, testSiteId)
    expect(team).toBeNull()

    // Reset testTeamId so cleanup doesn't try to delete again
    testTeamId = 0
  })

  test('should return null for non-existent team', async () => {
    const team = await dbService.getTeamById(99999, testSiteId)
    expect(team).toBeNull()
  })
})

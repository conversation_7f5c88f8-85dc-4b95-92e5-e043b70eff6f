/**
 * @jest-environment jsdom
 */

import { executeFormTool, getFormToolDefinitions } from '../form-tools';
import FormHandler from '../form-handler';

// Mock DOM setup
const createTestForm = () => {
  document.body.innerHTML = `
    <form id="testForm">
      <input type="text" id="firstName" name="firstName" required placeholder="First Name" />
      <input type="email" id="email" name="email" required placeholder="Email" />
      <input type="password" id="password" name="password" required placeholder="Password" />
      <input type="number" id="age" name="age" min="18" max="100" placeholder="Age" />
      <input type="checkbox" id="newsletter" name="notifications" value="newsletter" />
      <input type="radio" id="planBasic" name="plan" value="basic" required />
      <input type="radio" id="planPremium" name="plan" value="premium" required />
      <select id="country" name="country" required>
        <option value="">Select Country</option>
        <option value="us">United States</option>
        <option value="ca">Canada</option>
        <option value="uk">United Kingdom</option>
      </select>
      <select id="interests" name="interests" multiple>
        <option value="tech">Technology</option>
        <option value="sports">Sports</option>
        <option value="music">Music</option>
      </select>
      <textarea id="bio" name="bio" placeholder="Tell us about yourself"></textarea>
      <button type="submit" id="submitBtn">Submit</button>
    </form>
  `;
};

describe('Form Tools', () => {
  beforeEach(() => {
    createTestForm();
  });

  afterEach(() => {
    document.body.innerHTML = '';
  });

  describe('getFormToolDefinitions', () => {
    it('should return all form tool definitions', () => {
      const definitions = getFormToolDefinitions();
      
      expect(definitions).toHaveLength(8);
      expect(definitions.map(d => d.function.name)).toEqual([
        'query_elements',
        'fill_field',
        'select_option',
        'get_field_value',
        'get_form_data',
        'validate_form',
        'get_empty_required_fields',
        'get_form_summary'
      ]);
    });

    it('should have proper tool definition structure', () => {
      const definitions = getFormToolDefinitions();
      
      definitions.forEach(def => {
        expect(def).toHaveProperty('type', 'function');
        expect(def.function).toHaveProperty('name');
        expect(def.function).toHaveProperty('description');
        expect(def.function).toHaveProperty('parameters');
        expect(def.function.parameters).toHaveProperty('type', 'object');
        expect(def.function.parameters).toHaveProperty('properties');
      });
    });
  });

  describe('query_elements tool', () => {
    it('should query elements by ID', async () => {
      const result = await executeFormTool('query_elements', {
        selector: 'firstName',
        queryType: 'id'
      });

      expect(result.success).toBe(true);
      expect(result.result.found).toBe(true);
      expect(result.result.count).toBe(1);
      expect(result.result.elements[0].id).toBe('firstName');
    });

    it('should query elements by CSS selector', async () => {
      const result = await executeFormTool('query_elements', {
        selector: 'input[type="text"]'
      });

      expect(result.success).toBe(true);
      expect(result.result.found).toBe(true);
      expect(result.result.count).toBe(1);
    });

    it('should query elements by name', async () => {
      const result = await executeFormTool('query_elements', {
        selector: 'firstName',
        queryType: 'name'
      });

      expect(result.success).toBe(true);
      expect(result.result.found).toBe(true);
      expect(result.result.elements[0].name).toBe('firstName');
    });
  });

  describe('fill_field tool', () => {
    it('should fill text input', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#firstName',
        value: 'John Doe'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value).toBe('John Doe');
      
      const input = document.getElementById('firstName') as HTMLInputElement;
      expect(input.value).toBe('John Doe');
    });

    it('should fill email input', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#email',
        value: '<EMAIL>'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      
      const input = document.getElementById('email') as HTMLInputElement;
      expect(input.value).toBe('<EMAIL>');
    });

    it('should check checkbox', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#newsletter',
        value: 'true'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      
      const checkbox = document.getElementById('newsletter') as HTMLInputElement;
      expect(checkbox.checked).toBe(true);
    });

    it('should select radio button', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#planPremium',
        value: 'true'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      
      const radio = document.getElementById('planPremium') as HTMLInputElement;
      expect(radio.checked).toBe(true);
    });
  });

  describe('select_option tool', () => {
    it('should select single option', async () => {
      const result = await executeFormTool('select_option', {
        selector: '#country',
        values: ['us']
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      
      const select = document.getElementById('country') as HTMLSelectElement;
      expect(select.value).toBe('us');
    });

    it('should select multiple options', async () => {
      const result = await executeFormTool('select_option', {
        selector: '#interests',
        values: ['tech', 'music']
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      
      const select = document.getElementById('interests') as HTMLSelectElement;
      const selectedValues = Array.from(select.selectedOptions).map(opt => opt.value);
      expect(selectedValues).toEqual(['tech', 'music']);
    });
  });

  describe('get_field_value tool', () => {
    it('should get text input value', async () => {
      const input = document.getElementById('firstName') as HTMLInputElement;
      input.value = 'Test Value';

      const result = await executeFormTool('get_field_value', {
        selector: '#firstName'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value).toBe('Test Value');
    });

    it('should get checkbox state', async () => {
      const checkbox = document.getElementById('newsletter') as HTMLInputElement;
      checkbox.checked = true;

      const result = await executeFormTool('get_field_value', {
        selector: '#newsletter'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value).toBe(true);
    });
  });

  describe('get_form_data tool', () => {
    it('should get all form data', async () => {
      // Fill some form fields
      (document.getElementById('firstName') as HTMLInputElement).value = 'John';
      (document.getElementById('email') as HTMLInputElement).value = '<EMAIL>';
      (document.getElementById('newsletter') as HTMLInputElement).checked = true;

      const result = await executeFormTool('get_form_data', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value).toEqual({
        firstName: 'John',
        email: '<EMAIL>',
        notifications: 'newsletter'
      });
    });
  });

  describe('validate_form tool', () => {
    it('should validate form and return invalid fields', async () => {
      const result = await executeFormTool('validate_form', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value.isValid).toBe(false);
      expect(result.result.value.invalidFields.length).toBeGreaterThan(0);
    });

    it('should validate form as valid when all required fields are filled', async () => {
      // Fill required fields
      (document.getElementById('firstName') as HTMLInputElement).value = 'John';
      (document.getElementById('email') as HTMLInputElement).value = '<EMAIL>';
      (document.getElementById('password') as HTMLInputElement).value = 'password123';
      (document.getElementById('planBasic') as HTMLInputElement).checked = true;
      (document.getElementById('country') as HTMLSelectElement).value = 'us';

      const result = await executeFormTool('validate_form', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(true);
      expect(result.result.value.isValid).toBe(true);
      expect(result.result.value.invalidFields).toHaveLength(0);
    });
  });

  describe('get_empty_required_fields tool', () => {
    it('should return empty required fields', async () => {
      const result = await executeFormTool('get_empty_required_fields', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.emptyRequiredFields.length).toBeGreaterThan(0);
      expect(result.result.formValid).toBe(false);
      
      const fieldNames = result.result.emptyRequiredFields.map((f: any) => f.name);
      expect(fieldNames).toContain('firstName');
      expect(fieldNames).toContain('email');
      expect(fieldNames).toContain('password');
    });

    it('should return empty array when all required fields are filled', async () => {
      // Fill all required fields
      (document.getElementById('firstName') as HTMLInputElement).value = 'John';
      (document.getElementById('email') as HTMLInputElement).value = '<EMAIL>';
      (document.getElementById('password') as HTMLInputElement).value = 'password123';
      (document.getElementById('planBasic') as HTMLInputElement).checked = true;
      (document.getElementById('country') as HTMLSelectElement).value = 'us';

      const result = await executeFormTool('get_empty_required_fields', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.emptyRequiredFields).toHaveLength(0);
      expect(result.result.formValid).toBe(true);
    });
  });

  describe('get_form_summary tool', () => {
    it('should return comprehensive form summary', async () => {
      // Fill some fields
      (document.getElementById('firstName') as HTMLInputElement).value = 'John';
      (document.getElementById('email') as HTMLInputElement).value = '<EMAIL>';

      const result = await executeFormTool('get_form_summary', {
        formSelector: '#testForm'
      });

      expect(result.success).toBe(true);
      expect(result.result.fields).toBeDefined();
      expect(result.result.totalFields).toBeGreaterThan(0);
      expect(result.result.requiredFields).toBeGreaterThan(0);
      expect(result.result.filledFields).toBe(2);
      expect(result.result.formValid).toBe(false);
      
      // Check that field information is detailed
      const firstNameField = result.result.fields.find((f: any) => f.name === 'firstName');
      expect(firstNameField).toBeDefined();
      expect(firstNameField.value).toBe('John');
      expect(firstNameField.required).toBe(true);
      expect(firstNameField.type).toBe('text');
    });
  });

  describe('Error handling', () => {
    it('should handle unknown tool names', async () => {
      const result = await executeFormTool('unknown_tool', {});

      expect(result.success).toBe(false);
      expect(result.error).toBe('Unknown tool: unknown_tool');
    });

    it('should handle invalid selectors', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#nonexistent',
        value: 'test'
      });

      expect(result.success).toBe(true);
      expect(result.result.success).toBe(false);
      expect(result.result.message).toContain('Element not found');
    });

    it('should handle malformed parameters', async () => {
      const result = await executeFormTool('fill_field', {
        selector: '#firstName'
        // Missing value parameter
      });

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
});

/**
 * Browser-side form manipulation tools for AI chat widget
 * These tools allow the AI to interact with web forms through natural language
 */

import FormHandler from './form-handler';

export interface FormTool {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  execute: (params: any) => Promise<any>;
}

export interface ToolExecutionResult {
  success: boolean;
  result: any;
  error?: string;
}

/**
 * Registry of available form manipulation tools
 */
export const formTools: FormTool[] = [
  {
    name: 'query_elements',
    description: 'Query DOM elements using CSS selectors, IDs, names, classes, or XPath. Use this to find elements on the page.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'CSS selector, ID (#id), class (.class), name ([name="value"]), or XPath to find elements'
        },
        queryType: {
          type: 'string',
          enum: ['css', 'id', 'name', 'class', 'tag', 'xpath'],
          description: 'Type of query to perform (default: css)'
        }
      },
      required: ['selector']
    },
    execute: async (params) => {
      const { selector, queryType = 'css' } = params;
      
      try {
        let result;
        switch (queryType) {
          case 'id':
            result = FormHandler.getElementById(selector);
            break;
          case 'name':
            result = FormHandler.getElementsByName(selector);
            break;
          case 'class':
            result = FormHandler.getElementsByClass(selector);
            break;
          case 'tag':
            result = FormHandler.getElementsByTag(selector);
            break;
          case 'xpath':
            result = FormHandler.getElementsByXPath(selector);
            break;
          default:
            result = FormHandler.queryElements(selector);
        }
        
        return {
          success: true,
          result: result
        };
      } catch (error) {
        return {
          success: false,
          result: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
  },

  {
    name: 'fill_field',
    description: 'Fill a form field with a specified value. Works with text inputs, textareas, selects, checkboxes, and radio buttons.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'CSS selector to identify the form field (e.g., "#email", "[name=\'firstName\']", ".input-class")'
        },
        value: {
          type: 'string',
          description: 'Value to fill in the field. For checkboxes/radio: "true"/"false" or "1"/"0"'
        }
      },
      required: ['selector', 'value']
    },
    execute: async (params) => {
      const { selector, value } = params;
      return {
        success: true,
        result: FormHandler.fillField(selector, value)
      };
    }
  },

  {
    name: 'select_option',
    description: 'Select option(s) in a dropdown or multi-select element.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'CSS selector for the select element'
        },
        values: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of option values or text to select'
        }
      },
      required: ['selector', 'values']
    },
    execute: async (params) => {
      const { selector, values } = params;
      return {
        success: true,
        result: FormHandler.selectOption(selector, values)
      };
    }
  },

  {
    name: 'click_element',
    description: 'Click on an element such as buttons, links, or clickable form elements.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'CSS selector for the element to click'
        }
      },
      required: ['selector']
    },
    execute: async (params) => {
      const { selector } = params;
      return {
        success: true,
        result: FormHandler.clickElement(selector)
      };
    }
  },

  {
    name: 'get_field_value',
    description: 'Get the current value of a form field.',
    parameters: {
      type: 'object',
      properties: {
        selector: {
          type: 'string',
          description: 'CSS selector for the form field'
        }
      },
      required: ['selector']
    },
    execute: async (params) => {
      const { selector } = params;
      return {
        success: true,
        result: FormHandler.getFieldValue(selector)
      };
    }
  },

  {
    name: 'get_form_data',
    description: 'Get all data from a form as a key-value object.',
    parameters: {
      type: 'object',
      properties: {
        formSelector: {
          type: 'string',
          description: 'CSS selector for the form element (default: "form")'
        }
      },
      required: []
    },
    execute: async (params) => {
      const { formSelector = 'form' } = params;
      return {
        success: true,
        result: FormHandler.getFormData(formSelector)
      };
    }
  },

  {
    name: 'validate_form',
    description: 'Validate a form and get information about invalid fields.',
    parameters: {
      type: 'object',
      properties: {
        formSelector: {
          type: 'string',
          description: 'CSS selector for the form element (default: "form")'
        }
      },
      required: []
    },
    execute: async (params) => {
      const { formSelector = 'form' } = params;
      return {
        success: true,
        result: FormHandler.validateForm(formSelector)
      };
    }
  },

  {
    name: 'get_empty_required_fields',
    description: 'Get a list of required fields that are currently empty or invalid.',
    parameters: {
      type: 'object',
      properties: {
        formSelector: {
          type: 'string',
          description: 'CSS selector for the form element (default: "form")'
        }
      },
      required: []
    },
    execute: async (params) => {
      const { formSelector = 'form' } = params;
      
      try {
        const form = document.querySelector(formSelector) as HTMLFormElement;
        if (!form) {
          return {
            success: false,
            result: null,
            error: `Form not found: ${formSelector}`
          };
        }

        const emptyFields: Array<{
          selector: string;
          name: string;
          type: string;
          label: string;
          required: boolean;
          isEmpty: boolean;
          isInvalid: boolean;
        }> = [];

        const formElements = form.querySelectorAll('input, select, textarea');
        formElements.forEach(element => {
          const el = element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
          
          if (el.required) {
            const isEmpty = !el.value || (el instanceof HTMLInputElement && el.type === 'checkbox' && !el.checked);
            const isInvalid = !el.checkValidity();
            
            if (isEmpty || isInvalid) {
              // Try to find associated label
              let label = '';
              if (el.id) {
                const labelEl = document.querySelector(`label[for="${el.id}"]`);
                if (labelEl) label = labelEl.textContent?.trim() || '';
              }
              if (!label && el.name) {
                const labelEl = document.querySelector(`label[for="${el.name}"]`);
                if (labelEl) label = labelEl.textContent?.trim() || '';
              }
              
              emptyFields.push({
                selector: el.id ? `#${el.id}` : el.name ? `[name="${el.name}"]` : el.tagName.toLowerCase(),
                name: el.name || el.id || '',
                type: el instanceof HTMLInputElement ? el.type : el.tagName.toLowerCase(),
                label: label || el.placeholder || 'Unknown field',
                required: el.required,
                isEmpty,
                isInvalid
              });
            }
          }
        });

        return {
          success: true,
          result: {
            emptyRequiredFields: emptyFields,
            count: emptyFields.length,
            formValid: emptyFields.length === 0
          }
        };
      } catch (error) {
        return {
          success: false,
          result: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
  },

  {
    name: 'get_form_summary',
    description: 'Get a comprehensive summary of the form including all fields, their current values, and validation status.',
    parameters: {
      type: 'object',
      properties: {
        formSelector: {
          type: 'string',
          description: 'CSS selector for the form element (default: "form")'
        }
      },
      required: []
    },
    execute: async (params) => {
      const { formSelector = 'form' } = params;
      
      try {
        const form = document.querySelector(formSelector) as HTMLFormElement;
        if (!form) {
          return {
            success: false,
            result: null,
            error: `Form not found: ${formSelector}`
          };
        }

        const fields: Array<{
          selector: string;
          name: string;
          type: string;
          label: string;
          value: any;
          required: boolean;
          valid: boolean;
          placeholder?: string;
        }> = [];

        const formElements = form.querySelectorAll('input, select, textarea');
        formElements.forEach(element => {
          const el = element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
          
          // Get label
          let label = '';
          if (el.id) {
            const labelEl = document.querySelector(`label[for="${el.id}"]`);
            if (labelEl) label = labelEl.textContent?.trim() || '';
          }
          
          // Get value based on element type
          let value: any = '';
          if (el instanceof HTMLInputElement) {
            if (el.type === 'checkbox' || el.type === 'radio') {
              value = el.checked;
            } else {
              value = el.value;
            }
          } else if (el instanceof HTMLSelectElement) {
            if (el.multiple) {
              value = Array.from(el.selectedOptions).map(opt => opt.value);
            } else {
              value = el.value;
            }
          } else {
            value = el.value;
          }
          
          fields.push({
            selector: el.id ? `#${el.id}` : el.name ? `[name="${el.name}"]` : el.tagName.toLowerCase(),
            name: el.name || el.id || '',
            type: el instanceof HTMLInputElement ? el.type : el.tagName.toLowerCase(),
            label: label || el.placeholder || 'Unknown field',
            value,
            required: el.required,
            valid: el.checkValidity(),
            placeholder: el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement ? el.placeholder : undefined
          });
        });

        const validationResult = FormHandler.validateForm(formSelector);
        
        return {
          success: true,
          result: {
            fields,
            totalFields: fields.length,
            requiredFields: fields.filter(f => f.required).length,
            filledFields: fields.filter(f => f.value && f.value !== '').length,
            validFields: fields.filter(f => f.valid).length,
            formValid: validationResult.value?.isValid || false,
            invalidFields: validationResult.value?.invalidFields || []
          }
        };
      } catch (error) {
        return {
          success: false,
          result: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
  }
];

/**
 * Execute a form tool by name
 */
export async function executeFormTool(toolName: string, parameters: any): Promise<ToolExecutionResult> {
  const tool = formTools.find(t => t.name === toolName);
  
  if (!tool) {
    return {
      success: false,
      result: null,
      error: `Unknown tool: ${toolName}`
    };
  }

  try {
    return await tool.execute(parameters);
  } catch (error) {
    return {
      success: false,
      result: null,
      error: error instanceof Error ? error.message : 'Tool execution failed'
    };
  }
}

/**
 * Get tool definitions for the AI (without execute functions)
 */
export function getFormToolDefinitions() {
  return formTools.map(tool => ({
    type: 'function',
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }
  }));
}

export default {
  formTools,
  executeFormTool,
  getFormToolDefinitions
};
